# 算法优化

## MultiSensorDataFusion算法功能开发和验证阶段


1、纯地面雷达虚警过滤逻辑：
（1）若一个目标被识别为纯地面目标，
（2）判断其周边（500m内）不存在ads-b，
（3）再判断周边200米范围内是否满足a或b的条件，则判断该目标为虚警；
     a. 速度相近、方位相近，轨迹持续时间更长的同设备的纯地面目标（未融合状态);
         b. 速度相近、方位相近，同设备地面雷达目标为融合目标（不同设备融合，同设备融合）；

#### 更新时间：2025.07.15

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.5_20250715
LogDebug("algorithm start 0715.1100!");
1. 修改参数文件：
		"fragmented_distance_thres": 201,
		"fragmented_speed_dis_thres": 10
2. 速度有15%的阈值修改为差值10m/s
3. 修复地面雷达和地面雷达融合置信度叠加错误的问题;
4. 地面雷达融合成功后将各自的置信度设置为0，最后输出的时候融合目标的置信度设置为100;
5. 增加目标添加到缓存时的日志打印;

当前逻辑是：地面雷达和地面雷达融合成功后，将地面雷达的置信度设置为0，地面雷达和地面雷达融合失败后（如其中一个目标消失），会导致保留的目标置信度从0开始增长，3s后置信度增长到30才会显示，在页面中的结果是，目标的id不会，但是会导致目标点在3s内不更新

#### 更新时间：2025.07.12

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.5_20250712
(algorithm start 0712.1754!)
1. 解决由于飞机缓行时，ads-b数据发送时长由500ms增加到5000ms，导致融合id断批，将FusionParam.json中的adsB_keep_time_thres修改为6000;
2. 增加民航飞机地面雷达分裂的虚检过滤的功能，将置信度设置为0;
3. 在FusionParam.json文件中增加fragmented_distance_thres和fragmented_speed_dis_thres参数;

#### 更新时间：2025.07.11

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.4_20250711
(algorithm start 0711.1200!)
1. 增加针对adsB的异常数据过滤功能，针对飞机静止时偶尔由于adsB数据异常导致的轨迹跳变现象;
2. 简化日志打印;
3. 修改两个轨迹速度均为0时，相似度赋值为0;

#### 更新时间：2025.07.10

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.4_20250710
(algorithm start 0709.1350!)
1. 更新角度的归一化方式为直接使用cos;
2. 增加计算相似度函数的打印;

#### 更新时间：2025.07.09

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.4_20250709
(algorithm start 0709.1350!)
1. 调整匹配算法中速度的获取方式,由输入数据获取修改为通过对齐之后的点的经纬度计算,避免输入数据速度不准的影响;
2. 修改缓存中速度的计算方式,算法输出的数据中的速度和输入时一样,不做修改;
3. 调整轨迹的速度一致性判断方式;
4. 统一不同传感器之间的距离匹配误差为80m,为最长客机的机身长度;

#### 更新时间：2025.07.08

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.3_20250708
(algorithm start 0708.1500)
1. 通过经纬度计算adsB每个点的速度;
2. 计算相似度的时候增加速度约束,速度差距过大相似度设置为0,避免静止(位置轻微波动)的点和运动点的匹配;
3. 配置文件增加radar_confidence_factor_time(默认1s),1s增加一次雷达目标的执行度;

#### 更新时间：2025.07.07

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.2_20250707
(algorithm start 0707.2031)
1. 增加地面雷达和地面雷达的历史数据补充,解决低空切换到地面时id无法正确保持的问题;
2. 修改地面雷达的最大缓存为90;

#### 更新时间：2025.07.07

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.1_20250707
(algorithm start 0707.1026)
1. 修改数据保存为异步保存;

BUG: 对于连续两个静止的点的相似度计算可能存在问题

#### 更新时间：2025.07.05

#### 更新人： 王兴、贾涛

#### 算法版本：1.6.0_20250705

1. 增加多个传感器之间的二次最优匹配逻辑,解决偶尔误关联导致目标切换问题;

#### 更新时间：2025.07.05

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.9_20250705

1. 优化轨迹缓存器,空间复杂度从O(n)增加到O(2n),缓存更新的时间复杂度从O(n)降低到O(k),缓解在输入目标多的时候算法耗时增加严重的问题;
2. 增加二次最优匹配逻辑,缓解目标在拐弯处偶尔误关联导致目标切换问题(针对地面雷达之间);
3. 修改RadarAndAdsBParam的距离;
4. 修改计算相似度时变量没有赋值的问题;
5. main.cpp增加时间统计;
6. 修改目标置信度的修改逻辑,目标融合失败后,置信度设置为0;


#### 更新时间：2025.07.04

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.8_20250704

1. 增加相似度计算的缓存器,避免一次调用只传一个目标时对其他目标数据的重复计算,降低算法时间;
2. 增加Dockerfile,和docker中的编译脚本(docker_run.sh),使用ubuntu20.04的镜像进行编译;
3. 更新两两匹配结果的逻辑,给历史结果按照时间先后顺序增加权重,解决不同的历史结果计算的权重相同的问题;
4. 更新三个传感器的匹配逻辑,先将地面雷达和adsB融合, 再将低空雷达和adsB融合,最后将两个结果合并后更新最终结果;
5. 修改ID_before_fusion的赋值逻辑,将地面雷达去重的结果也保存到ID_before_fusion中,并确保融合id在ID_before_fusion中;

#### 更新时间：2025.07.04

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.7_20250704

1. 增加融合类型中地面雷达与地面雷达的融合状态;
2. ID_before_fusion中的融合字段中“#”字段的删除;


#### 更新时间：2025.07.02

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.7_20250703_1

1. 地面雷达之间的去重,直接从目标缓存中读取设备信息,支持1-2个设备,不使用设备加载的信息
2. 修改默认的缓存删除时间阈值
3. 增加分段函数,根据目标的速度设置不同的关联阈值,解决拐弯情况下的不关联问题
4. 计算轨迹相似性时增加速度的相似性,增加逻辑如果距离大于阈值则相似度设置为0
5. 根据不同传感器的帧率为不同的轨迹缓存设置不同的缓存长度


#### 更新时间：2025.07.02

#### 更新人： 贾涛、王兴

#### 算法版本：1.5.7_20250702_1

1. ID_before_fusion字段增加到4个，用于保存地面雷达去重前的ID
2. 输出打印增加ID_before_fusion
3. 修改轨迹的缓存大小为15帧，匹配的最小长度为3帧
4. 修改地面雷达去重的id保留规则，可以正确保留和最终结果匹配的id
5. 修改MAXFLOAT为FLT_MAX

#### 更新时间：2025.07.02

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.2_20250701_2

1. 修改三个传感器融合的逻辑,先将两两融合的结果进行融合，再更新历史缓存
2. 修改轨迹匹配中距离和角度计算变量没有正确赋值的问题
3. 修改output_fusion_result函数中无法正确获地面雷达id的问题
4. 将字符串匹配、复制等操作统一封装到comm_tools.h，修改target_repeat_alg.cpp和fusion_multi_alg_kuerle.cpp中相关字符串匹配和赋值的操作

#### 更新时间：2025.07.01

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.6_20250701

1. 为解决jave调用延时问题，将Multisensor_Fusion_Compute()方法中的输入数据的数据大小由256修改为1，现在为频谱、低空雷达、协议解析、地面雷达 、ads-b为1， 融合输出都为4；
2. 解决了地面雷达融合时，仅处理地面雷达数量为2的情况；
3. 解决output_fusion_result()函数中将input_target_ID重新赋值的问题；

#### 更新时间：2025.06.30

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.2_20250630_1

1. 修改多个地面雷达目标去重后再输出导致找不到目标的bug


#### 更新时间：2025.06.30

#### 更新人： 王兴、贾涛

#### 算法版本：1.5.2_20250630

1. 增加调试信息，修改可能导致数组越界的代码
2. 缓存数据过滤掉空的输入
3. 未启用RGRA融合

#### 更新时间：2025.06.28

#### 更新人： 王兴

#### 算法版本：1.5.2_20250628

1. 修改地面雷达与地面雷达历史缓存更新时的匈牙利匹配算法；
2. 融合类型为三种时，修改融合的策略；

#### 更新时间：2025.06.28

#### 更新人： 贾涛、王兴

#### 算法版本：1.5.0_20250628_2

1. 进行了两种传感器的数据融合测试，增加匈牙利匹配算法

#### 更新时间：2025.06.27

#### 更新人： 王兴

#### 算法版本：1.5.1_20250627

1. 输出融合数据的保存（非低空雷达融合目标）；
2. 注释掉融合代码，直接输出输入数据，用于网页端调试；

#### 更新时间：2025.06.26

#### 更新人： 贾涛、王兴

#### 算法版本：1.5.0_20250626

1. 实现库尔勒机场的去重和融合算法
2. 修改头文件，增加Ads-B的设备信息

TODO：待通过数据调试

#### 更新时间：2025.06.23

#### 更新人： 王兴

#### 算法版本：1.4.4_20250623

1. 在fusion_multi_alg.cpp中的fusion_droneID_radar()方法中，解决vector索引越界导致的潜在段错误，导致的算法崩溃；


#### 更新时间：2025.06.19

#### 更新人： 王兴、贾涛

#### 算法版本：1.3.14_20250619

**调试方式修改**
1. AlgLog.h 增加 "#define AlgTest   // 测试模式" 控制是否是测试仿真模式
2. 修改get_device_timestamp函数，增加#ifdef AlgTest ,导入AlgLog.h

**协议解析和雷达融合算法修改**
1. 修改最近3帧的航向默认为 drone_direct_similarity = false； 避免不足三帧的时候匹配成功，减少误关联,同时drone_direct_similarity的计算采用步骤2.2中对齐的数据进行计算
2. 计算协议解析和雷达轨迹的相似性
   - 定义轨迹详细性dis_similarity = false 
   - 增加get_droneID_radar_align_data()方法，对协议解析数据和雷达数据进行对齐，并且按照时间差进行插值
   - 增加calculate_droneID_radar_similarity()方法，计算对齐后的协议解析和雷达轨迹的距离和形状相似性(待确定是否存在其他更有效方法)
   - 修改fusion_droneID_radar()方法，增加轨迹相似性判断
3. 由圆形关联门修改为矩形关联门 通过GATE_LENGTH和GATE_WEIDTH两个参数控制,解决了横向误关联问题
4. 增加"std::vector<SpectrumPositioningTargetInfo>  droneID_single_data_target_vec;     //存储器 保存处理倒退问题后的数据"

TODO: 通过可视化的方式验证步骤2.2
TODO: 通过数据的时间控制缓存的长度而不使用固定帧数（主要针对协议解析）

存在问题：O4协议解析数据延迟太大，频繁出现超过（6-20s）以内的延迟，导致雷达目标和协议解析目标融合不成功。

#### 更新时间：2025.06.16

#### 更新时间：2025.06.16

#### 更新人： 贾涛

#### 算法版本：1.3.14_20250614

1. 修改save_target_to_json_file.cpp中的保存json函数，增加system_time字段
2. 修改main.py，读取json中保存的system_time作为仿真时的系统时间
3. 编译动态库的时候需要修改get_device_timestamp函数

#### 更新时间：2025.06.16

#### 更新人：王兴

#### 算法版本：1.3.14_20250614

1. 解决O4协议解析后无人机定位信号回退的问题；

* 原因：O4协议解析的无人机定位信息由于协议解析异步和网络延迟，不同无人机信息到达时间顺序存在差异；
* 解决办法：轨迹中连续2帧的方向相似，则更新无人机位置信息；否则不更新；

2. 解决融合类型为雷达时的误关联，融合类型为雷达时，仅考虑雷达ID相同的情况。

3. 解决融合类型为协议解析+雷达时误关联问题，
   - 融合类型为雷达和协议解析时，协议解析无人机速度小于0.5m/s，则不与雷达目标融合匹配；
   - 大于0.5m时，雷达轨迹与协议解析轨迹的方向小于90度，则匹配；
   - 协议解析和雷达信息间寻找最优解；
