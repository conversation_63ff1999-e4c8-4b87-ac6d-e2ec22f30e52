#include "fusion_multi_alg_kuerle.h"
#include <cstring>
#include <algorithm>
#include <algorithm> // std::max

extern int debug_count;

void FusionMultiAlgKuerle::ground_radar_target_arr_remove_repeat(std::vector<GroundRadarTarget> &ground_radar_target_arr_,
                                                                 std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr_,
                                                                 MultiSensorInfoArr &multi_sensor_info_arr_)
{
    // 将地面雷达的数组按照设备id拆分成两个数组
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_1;
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_2;
    // ground_radar_target_arr_split(ground_radar_target_arr_, multi_sensor_info_arr_, repeat_target_arr_1, repeat_target_arr_2);
    ground_radar_target_arr_split(ground_radar_target_arr_, repeat_target_arr_1, repeat_target_arr_2);

    // 两个地面雷达的去重
    fusiontype_param_.fusiontype = GroundRadar;
    target_repeat_alg_.remove_repeat(repeat_target_arr_1, repeat_target_arr_2, fusiontype_param_, repeat_gr2gr_result);

    // 解析去重结果，将去重后的目标添加到输出数组中
    ground_radar_target_arr_add_repeat_result(repeat_gr2gr_result, ground_radar_target_arr_, ground_radar_repeat_target_arr_);
}

/**
 * @brief multi_sensor_target_arr_remove_repeat - 多个传感器的目标去重 修改成员变量mult_fusion_result_arr_
 * @param  low_altitude_radar_target_arr -input ,低空雷达目标信息传入
 * @param  ground_radar_repeat_target_arr -input ,地面雷达去重目标信息传入
 * @param  adsB_target_arr -input ,ADS-B目标信息传入
 * @details
 */
void FusionMultiAlgKuerle::multi_sensor_target_arr_remove_repeat(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr_,
                                                                 const std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr_,
                                                                 const std::vector<AdsBPositioningTarget> &adsB_target_arr_)
{
    // 1. 确定融合类型
    FusionType fusion_type = Radar;
    int ret = get_mult_fusion_type(low_altitude_radar_target_arr_, ground_radar_repeat_target_arr_, adsB_target_arr_, fusion_type);
    if (ret < 0)
    {
        LogError("Get multi sensor fusion type failed {}", static_cast<int>(fusion_type));
        return;
    }

    // 2. 根据不同的融合类型确定并不同的融合方法
    LogInfo("Multi sensor fusion type: {}", static_cast<int>(fusion_type));
    switch (fusion_type)
    {
    case Radar:
        // 低空雷达去重
        remove_repeat_Radar(low_altitude_radar_target_arr_, repeat_mult_result);
        break;

    case GroundRadar:
        // 地面雷达去重
        remove_repeat_GroundRadar(ground_radar_repeat_target_arr_, repeat_mult_result);
        break;

    case AdsB:
        remove_repeat_AdsB(adsB_target_arr_, repeat_mult_result);
        break;

    case RadarAndGroundRadar:
        remove_repeat_RadarAndGroundRadar(low_altitude_radar_target_arr_, ground_radar_repeat_target_arr_, repeat_mult_result);
        break; 

    case RadarAndAdsB:
        remove_repeat_RadarAndAdsB(low_altitude_radar_target_arr_, adsB_target_arr_, repeat_mult_result);
        break;

    case GroundRadarAdsB:
        remove_repeat_GroundRadarAdsB(ground_radar_repeat_target_arr_, adsB_target_arr_, repeat_mult_result);
        break;

    case RGRA:
        remove_repeat_RadarAndGroundRadarAndAdsB(low_altitude_radar_target_arr_, ground_radar_repeat_target_arr_, adsB_target_arr_, repeat_mult_result);
        break;

    default:
        LogError("Invalid fusion type: {}", static_cast<int>(fusion_type));
        break;
    }
    target_repeat_alg_.check_fusion_result(repeat_mult_result);
}

int FusionMultiAlgKuerle::get_mult_fusion_type(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr_,
                                               const std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr_,
                                               const std::vector<AdsBPositioningTarget> &adsB_target_arr_,
                                               FusionType &fusion_type)
{
    // 根据每个数组的长度确定类型
    if (low_altitude_radar_target_arr_.size() > 0 && ground_radar_repeat_target_arr_.size() == 0 && adsB_target_arr_.size() == 0)
    {
        fusion_type = Radar;
    }
    else if (low_altitude_radar_target_arr_.size() == 0 && ground_radar_repeat_target_arr_.size() > 0 && adsB_target_arr_.size() == 0)
    {
        fusion_type = GroundRadar;
    }
    else if (low_altitude_radar_target_arr_.size() == 0 && ground_radar_repeat_target_arr_.size() == 0 && adsB_target_arr_.size() > 0)
    {
        fusion_type = AdsB;
    }
    else if (low_altitude_radar_target_arr_.size() > 0 && ground_radar_repeat_target_arr_.size() > 0 && adsB_target_arr_.size() == 0)
    {
        fusion_type = RadarAndGroundRadar;
    }
    else if (low_altitude_radar_target_arr_.size() > 0 && ground_radar_repeat_target_arr_.size() == 0 && adsB_target_arr_.size() > 0)
    {
        fusion_type = RadarAndAdsB;
    }
    else if (low_altitude_radar_target_arr_.size() == 0 && ground_radar_repeat_target_arr_.size() > 0 && adsB_target_arr_.size() > 0)
    {
        fusion_type = GroundRadarAdsB;
    }
    else if (low_altitude_radar_target_arr_.size() > 0 && ground_radar_repeat_target_arr_.size() > 0 && adsB_target_arr_.size() > 0)
    {
        fusion_type = RGRA;
    }
    else
    {
        LogError("Invalid fusion type: {}", static_cast<int>(fusion_type));
        return -1;
    }
    return 0;
}

/**
 * @brief ground_radar_target_arr_add_repeat_result - 将去重结果添加到地面雷达目标数组
 * @param repeat_result - 输入，去重结果数组
 * @param ground_radar_target_arr - 输入输出，地面雷达目标数组
 * @param ground_radar_repeat_target_arr - 输出，去重后的地面雷达目标数组
 *
 * 功能简介：根据去重结果处理地面雷达目标，避免在遍历过程中修改原始数组
 * 详细流程：
 * 1. 使用标志位数组标记需要移除的目标索引
 * 2. 遍历去重结果，查找匹配的目标并标记
 * 3. 将匹配的目标添加到去重数组，并更新目标ID
 * 4. 根据标志位数组移除已处理的目标
 */
void FusionMultiAlgKuerle::ground_radar_target_arr_add_repeat_result(const std::vector<TargetRepeatAlg::RepeatResultDouble> &repeat_result,
                                                                     std::vector<GroundRadarTarget> &ground_radar_target_arr,
                                                                     std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr)
{
    ground_radar_repeat_target_arr.clear();

    // 使用标志位数组标记需要移除的目标索引，避免在遍历过程中修改原始数组
    std::vector<bool> targets_to_remove(ground_radar_target_arr.size(), false);

    // 1. 遍历所有的去重结果
    for (const auto &repeat_result_ : repeat_result)
    {
        // 获取融合的id
        std::string target_ID1 = repeat_result_.target_ID1;
        FusionType fusiontype1 = repeat_result_.fusiontype1;
        std::string target_ID2 = repeat_result_.target_ID2;
        FusionType fusiontype2 = repeat_result_.fusiontype2;
        int64_t start_time = repeat_result_.start_time;

        bool id1_empty = (target_ID1.empty() || target_ID1[0] == '\0');
        bool id2_empty = (target_ID2.empty() || target_ID2[0] == '\0');

        if (id1_empty && id2_empty)
        {
            LogError("target_ID1 and target_ID2 are both null {}", 0);
        }
        else if (id1_empty && !id2_empty)
        {
            // 查找匹配target_ID2的目标
            for (size_t i = 0; i < ground_radar_target_arr.size(); ++i)
            {
                if (targets_to_remove[i]) continue; // 跳过已标记移除的目标

                if (safe_string_compare(ground_radar_target_arr[i].target_ID, target_ID2.c_str()))
                {
                    // 创建副本并修改ID
                    GroundRadarTarget target_copy = ground_radar_target_arr[i];
                    if(!safe_string_copy(target_copy.target_ID, repeat_result_.first_apperance_ID, sizeof(target_copy.target_ID)))
                    {
                        LogError("Target ID too long, truncated: {}", repeat_result_.first_apperance_ID);
                    }
                    ground_radar_repeat_target_arr.push_back(target_copy);
                    targets_to_remove[i] = true; // 标记为需要移除
                    break;
                }
            }
        }
        else if (!id1_empty && id2_empty)
        {
            // 查找匹配target_ID1的目标
            for (size_t i = 0; i < ground_radar_target_arr.size(); ++i)
            {
                if (targets_to_remove[i]) continue; // 跳过已标记移除的目标

                if (safe_string_compare(ground_radar_target_arr[i].target_ID, target_ID1.c_str()))
                {
                    // 创建副本并修改ID
                    GroundRadarTarget target_copy = ground_radar_target_arr[i];
                    if(!safe_string_copy(target_copy.target_ID, repeat_result_.first_apperance_ID, sizeof(target_copy.target_ID)))
                    {
                        LogError("Target ID too long, truncated: {}", repeat_result_.first_apperance_ID);
                    }
                    ground_radar_repeat_target_arr.push_back(target_copy);
                    targets_to_remove[i] = true; // 标记为需要移除
                    break;
                }
            }
        }
        else if (!id1_empty && !id2_empty)
        {
            // 保留存在时间长的目标
            size_t target1_index = SIZE_MAX, target2_index = SIZE_MAX;
            bool find1 = false, find2 = false;

            // 查找两个目标的索引
            for (size_t i = 0; i < ground_radar_target_arr.size(); ++i)
            {
                if (targets_to_remove[i]) continue; // 跳过已标记移除的目标

                if (!find1 && safe_string_compare(ground_radar_target_arr[i].target_ID, target_ID1.c_str()))
                {
                    target1_index = i;
                    find1 = true;
                }
                else if (!find2 && safe_string_compare(ground_radar_target_arr[i].target_ID, target_ID2.c_str()))
                {
                    target2_index = i;
                    find2 = true;
                }

                if (find1 && find2) break; // 两个都找到了，可以退出循环
            }

            if (find1 && find2)
            {
                // 计算两个目标的存在时间，选择存在时间更长的目标
                GroundRadarTarget &target_1 = ground_radar_target_arr[target1_index];
                GroundRadarTarget &target_2 = ground_radar_target_arr[target2_index];
                target_1.confidence_level = 0.0f;
                target_2.confidence_level = 0.0f;

                int64_t time1_diff = start_time - target_1.start_time;
                int64_t time2_diff = start_time - target_2.start_time;

                if (time1_diff > time2_diff)
                {
                    // 选择target_1，将target_2的历史数据补充到target_1的副本中
                    // 将target_2的历史数据补充到target_copy中
                    copy_history(target_1, target_2);
                    GroundRadarTarget target_copy = target_2;
                    if(!safe_string_copy(target_copy.target_ID, repeat_result_.first_apperance_ID, sizeof(target_copy.target_ID)))
                    {
                        LogError("Target ID too long, truncated: {}", repeat_result_.first_apperance_ID);
                    }
                    ground_radar_repeat_target_arr.push_back(target_copy);
                    targets_to_remove[target1_index] = true;
                }
                else
                {
                    // 将target_1的历史数据补充到target_copy中
                    copy_history(target_2, target_1);
                    // 选择target_2，将target_1的历史数据补充到target_2的副本中
                    GroundRadarTarget target_copy = target_1;
                    if(!safe_string_copy(target_copy.target_ID, repeat_result_.first_apperance_ID, sizeof(target_copy.target_ID)))
                    {
                        LogError("Target ID too long, truncated: {}", repeat_result_.first_apperance_ID);
                    }
                    ground_radar_repeat_target_arr.push_back(target_copy);
                    targets_to_remove[target2_index] = true;
                }
            }
            else
            {
                LogError("Can not find target {}({}) or {}({})", target_ID1, find1, target_ID2, find2);
            }
        }
    }
}

/**
 * @brief copy_history - 将src的缓存复制到dist
 * @param target_src - 输入，源目标
 * @param target_dist - 输入输出，目标
 * @details 如果target_dist的缓存没有达到最大值,并且缓存的最旧的时间大于target_src的最旧时间,则需要对target_dist的轨迹进行补全(将target_src中更早的数据添加到target_dist中)
 */
void FusionMultiAlgKuerle::copy_history(const GroundRadarTarget target_src,
                                        GroundRadarTarget& target_dist)
{
    // 检查两个目标的缓存是否为空
    if (target_src.target_catch.empty() || target_dist.target_catch.empty()) {
        LogError("Source({}) or destination({}) target cache is empty, cannot copy history", target_src.target_ID, target_dist.target_ID);
        return;
    }

    bool target_dist_is_full = target_dist.target_catch.full();
    // 如果目标的缓存已经满了,则不需要进行补全
    if(target_dist_is_full) return ;

    // 获取最旧的时间
    int64_t target_dist_oldest_time = target_dist.target_catch.front().uav_timestamp;  // 2025-07-07 14:05:35
    int64_t target_src_oldest_time = target_src.target_catch.front().uav_timestamp;   // 2025-07-07 14:05:24

    // 如果target_dist的最旧的时间小于等于target_src的最旧时间,则不需要进行补全
    if(target_dist_oldest_time <= target_src_oldest_time) return ;

    // 创建新的缓存，先添加src数据，再添加dist数据
    CircularBuffer<RadarTargetInfo> new_cache = target_src.target_catch;

    // 将new_cache中时间戳>=target_dist_oldest_time的删除掉 缓存的时间已经从小到大排序,逆序遍历删除
    for(size_t i = new_cache.size() - 1; i >= 0; --i) {
        if(new_cache[i].uav_timestamp >= target_dist_oldest_time) {
            new_cache.erase(i);
        } else {
            break;
        }
    }

    // 再添加原有的数据 此时如果缓存已满,则会自动删除头部的数据
    for (size_t i = 0; i < target_dist.target_catch.size() ; ++i) {
        new_cache.push_back(target_dist.target_catch[i]);
    }

    // 替换目标缓存
    target_dist.target_catch = new_cache;

    LogInfo("Successfully copied {} history records from source({}) to target({})",
            target_src.target_catch.size(), target_src.target_ID, target_dist.target_ID);
}

/**
 * @brief ground_radar_target_arr_split - 地面雷达目标数组按设备ID分组
 * @param ground_radar_target_arr_ - input, 地面雷达目标数组
 * @param multi_sensor_info_arr_ - input, 多传感器信息数组
 * @param repeat_target_arr_1 - output, 第一个雷达设备的目标信息数组
 * @param repeat_target_arr_2 - output, 第二个雷达设备的目标信息数组
 * @details
 * 1. 检查地面雷达设备数量是否为2
 * 2. 获取两个雷达设备的ID
 * 3. 遍历地面雷达目标数组，根据设备ID将目标分配到对应的输出数组
 * 4. 将GroundRadarTarget类型转换为TargetRepeatInfoArr类型
 * 5. 从目标的轨迹缓存中提取最新数据进行转换
 */
void FusionMultiAlgKuerle::ground_radar_target_arr_split(const std::vector<GroundRadarTarget> &ground_radar_target_arr_,
                                                               const MultiSensorInfoArr &multi_sensor_info_arr_,
                                                               std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr_1,
                                                               std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr_2)
{
    // 判断地面雷达的数量是否为2,只支持2
    if (multi_sensor_info_arr_.GRadarDeviceNum == 2)
    {
        // 获取两个地面雷达的设备ID
        std::string radar_device_id_1 = multi_sensor_info_arr_.GRadarDeviceInfoArr[0].RadarDeviceID;
        std::string radar_device_id_2 = multi_sensor_info_arr_.GRadarDeviceInfoArr[1].RadarDeviceID;

        // LogInfo("Split ground radar targets by device ID: {} and {}",
        //         radar_device_id_1.c_str(), radar_device_id_2.c_str());

        // 清空输出数组
        repeat_target_arr_1.clear();
        repeat_target_arr_2.clear();

        // 遍历地面雷达目标数组
        for (const auto &ground_target : ground_radar_target_arr_)
        {
            // 检查目标是否有有效数据
            if (ground_target.target_catch.empty())
            {
                LogError("Ground radar target %s has no trajectory data, skip", ground_target.target_ID);
                continue;
            }

            // 获取最新的轨迹数据来判断设备ID
            RadarTargetInfo latest_radar_info = ground_target.target_catch.get_newest();
            std::string target_device_id = latest_radar_info.radar_device_ID;

            // 创建TargetRepeatInfoArr对象
            TargetRepeatAlg::TargetRepeatInfoArr repeat_info_arr(ground_target.target_catch.capacity());

            // 复制目标ID和置信度
            strncpy(repeat_info_arr.target_ID, ground_target.target_ID, sizeof(repeat_info_arr.target_ID) - 1);
            repeat_info_arr.target_ID[sizeof(repeat_info_arr.target_ID) - 1] = '\0';
            repeat_info_arr.confidence = ground_target.confidence_level;
            repeat_info_arr.first_apperance_time = ground_target.start_time;

            // 从CircularBuffer中提取所有轨迹数据并转换为TargetRepeatInfo
            size_t trajectory_size = ground_target.target_catch.size();
            repeat_info_arr.target_repeat_info_arr.clear();

            for (size_t i = 0; i < trajectory_size; ++i)
            {
                RadarTargetInfo radar_info = ground_target.target_catch[i];

                // 创建TargetRepeatInfo并填充数据
                TargetRepeatAlg::TargetRepeatInfo repeat_info;

                // 复制目标ID
                strncpy(repeat_info.target_ID, radar_info.radar_uav_ID, sizeof(repeat_info.target_ID) - 1);
                repeat_info.target_ID[sizeof(repeat_info.target_ID) - 1] = '\0';

                // 转换坐标和其他信息
                repeat_info.target_longitude = radar_info.radar_uav_longitude;
                repeat_info.target_latitude = radar_info.radar_uav_latitude;
                repeat_info.target_altitude = radar_info.radar_uav_altitude;
                repeat_info.target_timestamp = radar_info.uav_timestamp;
                repeat_info.target_course = radar_info.radar_uav_course;
                repeat_info.target_speed = radar_info.radar_uav_speed;
                repeat_info.confidence = ground_target.confidence_level;

                repeat_info_arr.target_repeat_info_arr.push_back(repeat_info);
            }

            // 根据设备ID分配到对应的输出数组
            if (target_device_id == radar_device_id_1)
            {
                repeat_target_arr_1.push_back(repeat_info_arr);
                // LogInfo("Assigned target {} to radar device 1 ({})", ground_target.target_ID, radar_device_id_1.c_str());
            }
            else if (target_device_id == radar_device_id_2)
            {
                repeat_target_arr_2.push_back(repeat_info_arr);
                // LogInfo("Assigned target {} to radar device 2 ({})", ground_target.target_ID, radar_device_id_2.c_str());
            }
            else
            {
                LogError("Target %s device ID ({}) does not match any configured radar device, skip",
                         ground_target.target_ID, target_device_id.c_str());
            }
        }
    }
    else
    {
        LogWarn("Only support 2 ground radar, but got {}", multi_sensor_info_arr_.GRadarDeviceNum);
        // 创建TargetRepeatInfo并填充数据
        repeat_target_arr_1.clear();
        repeat_target_arr_2.clear();
        for (const auto &ground_target : ground_radar_target_arr_)
        {
            // 检查目标是否有有效数据
            if (ground_target.target_catch.empty())
            {
                LogError("Ground radar target %s has no trajectory data, skip", ground_target.target_ID);
                continue;
            }

            // 获取最新的轨迹数据来判断设备ID
            RadarTargetInfo latest_radar_info = ground_target.target_catch.get_newest();
            std::string target_device_id = latest_radar_info.radar_device_ID;

            // 创建TargetRepeatInfoArr对象
            TargetRepeatAlg::TargetRepeatInfoArr repeat_info_arr(ground_target.target_catch.capacity());

            // 复制目标ID和置信度
            bool key = safe_string_copy(repeat_info_arr.target_ID, ground_target.target_ID, sizeof(repeat_info_arr.target_ID));
            if (!key){
                LogError("Target ID too long, truncated: {}", ground_target.target_ID);
            }
            repeat_info_arr.confidence = ground_target.confidence_level;
            repeat_info_arr.first_apperance_time = ground_target.start_time;

            // 从CircularBuffer中提取所有轨迹数据并转换为TargetRepeatInfo
            size_t trajectory_size = ground_target.target_catch.size();
            repeat_info_arr.target_repeat_info_arr.clear();

            for (size_t i = 0; i < trajectory_size; ++i)
            {
                RadarTargetInfo radar_info = ground_target.target_catch[i];

                // 创建TargetRepeatInfo并填充数据
                TargetRepeatAlg::TargetRepeatInfo repeat_info;

                // 复制目标ID
                bool key = safe_string_copy(repeat_info.target_ID, radar_info.radar_uav_ID, sizeof(repeat_info.target_ID));
                if (!key){
                    LogError("Target ID too long, truncated: {}", radar_info.radar_uav_ID);
                }

                // 转换坐标和其他信息
                repeat_info.target_longitude = radar_info.radar_uav_longitude;
                repeat_info.target_latitude = radar_info.radar_uav_latitude;
                repeat_info.target_altitude = radar_info.radar_uav_altitude;
                repeat_info.target_timestamp = radar_info.uav_timestamp;
                repeat_info.target_course = radar_info.radar_uav_course;
                repeat_info.target_speed = radar_info.radar_uav_speed;
                repeat_info.confidence = ground_target.confidence_level;

                repeat_info_arr.target_repeat_info_arr.push_back(repeat_info);
            }
            repeat_target_arr_1.push_back(repeat_info_arr);
        }
    }
    LogInfo("Ground radar target split completed: device 1 has {} targets, device 2 has {} targets",
            repeat_target_arr_1.size(), repeat_target_arr_2.size());
}

/**
 * @brief ground_radar_target_arr_split - 地面雷达目标数组按设备ID分组
 * @param ground_radar_target_arr_ - input, 地面雷达目标数组
 * @param repeat_target_arr_1 - output, 第一个雷达设备的目标信息数组
 * @param repeat_target_arr_2 - output, 第二个雷达设备的目标信息数组
 * @details
 * 1. 从目标数组中提取所有设备ID
 * 2. 最多支持两个设备ID
 * 3. 遍历地面雷达目标数组，根据设备ID将目标分配到对应的输出数组
 * 4. 将GroundRadarTarget类型转换为TargetRepeatInfoArr类型
 * 5. 从目标的轨迹缓存中提取数据进行转换
 */
void FusionMultiAlgKuerle::ground_radar_target_arr_split(const std::vector<GroundRadarTarget> &ground_radar_target_arr_,
                                                               std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr_1,
                                                               std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr_2)
{
    // 清空输出数组
    repeat_target_arr_1.clear();
    repeat_target_arr_2.clear();
    
    // 从目标数组中提取所有设备ID
    std::vector<std::string> device_ids;

    // 遍历地面雷达目标数组
    for (const auto &ground_target : ground_radar_target_arr_)
    {
        // 检查目标是否有有效数据
        if (ground_target.target_catch.empty())
        {
            LogError("Ground radar target {} has no trajectory data, skip", ground_target.target_ID);
            continue;
        }
        
        // 获取最新的轨迹数据来判断设备ID
        RadarTargetInfo latest_radar_info = ground_target.target_catch.get_newest();
        std::string target_device_id = std::string(latest_radar_info.radar_device_ID);
        
        // 创建TargetRepeatInfoArr对象
        TargetRepeatAlg::TargetRepeatInfoArr repeat_info_arr(ground_target.target_catch.capacity());
        
        // 复制目标ID和置信度
        // 安全复制
        bool key = safe_string_copy(repeat_info_arr.target_ID, ground_target.target_ID, sizeof(repeat_info_arr.target_ID));
        if (!key){
            LogError("Target ID too long, truncated: {}", ground_target.target_ID);
        }
        repeat_info_arr.confidence = ground_target.confidence_level;
        repeat_info_arr.first_apperance_time = ground_target.start_time;
        
        // 从CircularBuffer中提取所有轨迹数据并转换为TargetRepeatInfo
        size_t trajectory_size = ground_target.target_catch.size();
        repeat_info_arr.target_repeat_info_arr.clear();
        
        for (size_t i = 0; i < trajectory_size; ++i)
        {
            RadarTargetInfo radar_info = ground_target.target_catch[i];
            
            // 创建TargetRepeatInfo并填充数据
            TargetRepeatAlg::TargetRepeatInfo repeat_info;
            
            // 复制目标ID
            // 安全复制
            bool key = safe_string_copy(repeat_info.target_ID, radar_info.radar_uav_ID, sizeof(repeat_info.target_ID));
            if (!key){
                LogError("Target ID too long, truncated: {}", radar_info.radar_uav_ID);
            }
            
            // 转换坐标和其他信息
            repeat_info.target_longitude = radar_info.radar_uav_longitude;
            repeat_info.target_latitude = radar_info.radar_uav_latitude;
            repeat_info.target_altitude = radar_info.radar_uav_altitude;
            repeat_info.target_timestamp = radar_info.uav_timestamp;
            repeat_info.target_course = radar_info.radar_uav_course;
            repeat_info.target_speed = radar_info.radar_uav_speed;
            repeat_info.confidence = ground_target.confidence_level;
            
            repeat_info_arr.target_repeat_info_arr.push_back(repeat_info);
        }
        
        // 根据设备ID分配到对应的输出数组
        // 检查设备ID是否已存在
        bool found = false;
        for (const auto &id : device_ids)
        {
            if (id == target_device_id)
            {
                found = true;
                break;
            }
        }
        // 如果设备ID不存在且数量小于2，则添加
        if (!found && device_ids.size() < 2)
        {
            device_ids.push_back(target_device_id);
        }
        // 如果设备ID不存在且数量大于等于2，则跳过
        else if (!found && device_ids.size() >= 2)
        {
            LogError("ground radar target {} has more than 2 devices, skip", ground_target.target_ID);
            continue;
        }
        
        // 设备数量必须大于0
        if (device_ids.size() == 0)
        {
            LogError("ground radar target {} has no device, skip", ground_target.target_ID);
            continue;
        }
        else if (device_ids.size() == 1)
        {
            // 只有一个设备ID，全部放入第一个数组
            repeat_target_arr_1.push_back(repeat_info_arr);
        }
        else
        {
            // 有两个设备ID，根据设备ID分配
            if (target_device_id == device_ids[0])
            {
                repeat_target_arr_1.push_back(repeat_info_arr);
            }
            else if (target_device_id == device_ids[1])
            {
                repeat_target_arr_2.push_back(repeat_info_arr);
            }
            else
            {
                // 设备ID不匹配，放入第一个数组
                LogError("Target {} device ID ({}) does not match any configured radar device, assigned to device 1",
                       ground_target.target_ID, target_device_id.c_str());
                continue;
            }
        }
    }
    
    LogInfo("Ground radar target split completed: device 1 has {} targets, device 2 has {} targets",
            repeat_target_arr_1.size(), repeat_target_arr_2.size());
}

/**
 * @brief remove_repeat_Radar - 低空雷达目标去重
 * @param low_altitude_radar_target_arr - input, 低空雷达目标信息数组
 * @param repeat_mult_result - output, 低空雷达数组ID对
 * @return 无返回值
 * @details
 * 0. 获取当前帧的低空雷达的ID对
 * 1. 对缓存历史ID对按照时间先后顺序进行排序
 * 2. 更新和删除缓存的历史ID对
 * 3. 将未匹配成功的目标插入到缓存的历史ID对
 */
void FusionMultiAlgKuerle::remove_repeat_Radar(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr,
                                               std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_radar_result)
{
    LogInfo("开始处理低空雷达目标去重，目标数量: {}", low_altitude_radar_target_arr.size());
    std::vector<TargetRepeatAlg::RepeatResultMult> current_repeat_radar_result;
    // 低空雷达的当前ID对赋值
    for (auto lradar : low_altitude_radar_target_arr)
    {
        TargetRepeatAlg::RepeatResultMult radar_id;
        memcpy(radar_id.target_ID_llr, lradar.target_ID, sizeof(radar_id.target_ID_llr));
        radar_id.start_time = lradar.target_catch.get_newest().uav_timestamp;
        memcpy(radar_id.first_apperance_ID, lradar.target_ID, sizeof(radar_id.first_apperance_ID));
        current_repeat_radar_result.push_back(radar_id);
    }
    std::sort(repeat_radar_result.begin(), repeat_radar_result.end(), [](const TargetRepeatAlg::RepeatResultMult &a, const TargetRepeatAlg::RepeatResultMult &b)
              {
                  return a.start_time < b.start_time; // 注意这里使用了小于号
              });
    remove_repeat_Single_Sensor(current_repeat_radar_result, repeat_radar_result, FusionType::Radar);
}

/**
 * @brief remove_repeat_GroundRadar - 地面雷达目标去重
 * @param ground_radar_repeat_target_arr - input, 地面雷达目标信息数组
 * @return 无返回值
 * @details
 */
void FusionMultiAlgKuerle::remove_repeat_GroundRadar(const std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr,
                                                     std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_gradar_result)
{
    LogInfo("开始处理地面雷达目标去重，目标数量: {}", ground_radar_repeat_target_arr.size());
    std::vector<TargetRepeatAlg::RepeatResultMult> current_repeat_gradar_result;
    for (auto gradar : ground_radar_repeat_target_arr)
    {
        TargetRepeatAlg::RepeatResultMult gradar_id;
        memcpy(gradar_id.target_ID_gr, gradar.target_ID, sizeof(gradar_id.target_ID_gr));
        gradar_id.start_time = gradar.target_catch.get_newest().uav_timestamp;
        memcpy(gradar_id.first_apperance_ID, gradar.target_ID, sizeof(gradar_id.first_apperance_ID));
        current_repeat_gradar_result.push_back(gradar_id);
    }
    std::sort(repeat_gradar_result.begin(), repeat_gradar_result.end(), [](const TargetRepeatAlg::RepeatResultMult &a, const TargetRepeatAlg::RepeatResultMult &b)
              {
                  return a.start_time < b.start_time; // 注意这里使用了小于号
              });
    remove_repeat_Single_Sensor(current_repeat_gradar_result, repeat_gradar_result, FusionType::GroundRadar);
}

/**
 * @brief remove_repeat_AdsB - AdsB目标去重
 * @param adsB_target_arr - input, AdsB目标信息数组
 * @param repeat_adsB_result - output, 更新后的缓存ID对
 * @details 对AdsB目标进行去重处理，输出处理信息
 */
void FusionMultiAlgKuerle::remove_repeat_AdsB(const std::vector<AdsBPositioningTarget> &adsB_repeat_target_arr,
                                              std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_adsB_result)
{
    LogInfo("开始处理AdsB目标去重，目标数量: {}", adsB_repeat_target_arr.size());
    std::vector<TargetRepeatAlg::RepeatResultMult> current_repeat_adsB_result;

    for (auto absB_t : adsB_repeat_target_arr)
    {
        TargetRepeatAlg::RepeatResultMult adsb_target;
        memcpy(adsb_target.target_ID_adsB, absB_t.target_ID, sizeof(adsb_target.target_ID_adsB));
        adsb_target.start_time = absB_t.target_catch.get_newest().cvl_timestamp;
        memcpy(adsb_target.first_apperance_ID, absB_t.target_ID, sizeof(adsb_target.first_apperance_ID));
        current_repeat_adsB_result.push_back(adsb_target);
    }

    std::sort(repeat_adsB_result.begin(), repeat_adsB_result.end(), [](const TargetRepeatAlg::RepeatResultMult &a, const TargetRepeatAlg::RepeatResultMult &b)
              {
                  return a.start_time < b.start_time; // 注意这里使用了小于号
              });
    remove_repeat_Single_Sensor(current_repeat_adsB_result, repeat_adsB_result, FusionType::AdsB);
}

void FusionMultiAlgKuerle::remove_repeat_RadarAndGroundRadar(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr,
                                                             const std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr,
                                                             std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_mult_result_)
{
    LogInfo("开始处理低空雷达和地面雷达联合去重，低空雷达目标数量: {}，地面雷达目标数量: {}",
            low_altitude_radar_target_arr.size(), ground_radar_repeat_target_arr.size());
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_1;
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_2;
    // 低空雷达转TargetRepeatInfoArr
    la_radar2repeat_target_arr(low_altitude_radar_target_arr, repeat_target_arr_1);
    // 地面雷达转TargetRepeatInfoArr
    ground_radar2repeat_target_arr(ground_radar_repeat_target_arr, repeat_target_arr_2);
    // 去重算法
    fusiontype_param_.fusiontype = RadarAndGroundRadar;
    target_repeat_alg_.remove_repeat(repeat_target_arr_1, repeat_target_arr_2, fusiontype_param_, repeat_mult_result_);
}

void FusionMultiAlgKuerle::remove_repeat_RadarAndAdsB(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr,
                                                      const std::vector<AdsBPositioningTarget> &adsB_target_arr,
                                                      std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_mult_result_)
{
    LogInfo("开始处理低空雷达和AdsB联合去重，低空雷达目标数量: {}，AdsB目标数量: {}",
            low_altitude_radar_target_arr.size(), adsB_target_arr.size());
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_1;
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_2;
    // 低空雷达转TargetRepeatInfoArr
    la_radar2repeat_target_arr(low_altitude_radar_target_arr, repeat_target_arr_1);
    // AdsB转TargetRepeatInfoArr
    adsB2repeat_target_arr(adsB_target_arr, repeat_target_arr_2);
    // 去重算法
    fusiontype_param_.fusiontype = RadarAndAdsB;
    target_repeat_alg_.remove_repeat(repeat_target_arr_1, repeat_target_arr_2, fusiontype_param_, repeat_mult_result_);
}

void FusionMultiAlgKuerle::remove_repeat_GroundRadarAdsB(const std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr,
                                                         const std::vector<AdsBPositioningTarget> &adsB_target_arr,
                                                         std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_mult_result_)
{
    LogInfo("开始处理地面雷达和AdsB联合去重，地面雷达目标数量: {}，AdsB目标数量: {}",
            ground_radar_repeat_target_arr.size(), adsB_target_arr.size());
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_1;
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_2;
    // 地面雷达转TargetRepeatInfoArr
    ground_radar2repeat_target_arr(ground_radar_repeat_target_arr, repeat_target_arr_1);
    // AdsB转TargetRepeatInfoArr
    adsB2repeat_target_arr(adsB_target_arr, repeat_target_arr_2);
    // 去重算法
    fusiontype_param_.fusiontype = GroundRadarAdsB;
    target_repeat_alg_.remove_repeat(repeat_target_arr_1, repeat_target_arr_2, fusiontype_param_, repeat_mult_result_);
}

// 方案2
// void FusionMultiAlgKuerle::remove_repeat_RadarAndGroundRadarAndAdsB(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr,
//                                                                     const std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr,
//                                                                     const std::vector<AdsBPositioningTarget> &adsB_target_arr,
//                                                                     std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_mult_result_)
// {
//     // 结构体转换
//     std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_1;
//     std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_2;
//     std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_1and2;
//     std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_3;
//     la_radar2repeat_target_arr(low_altitude_radar_target_arr, repeat_target_arr_1);
//     ground_radar2repeat_target_arr(ground_radar_repeat_target_arr, repeat_target_arr_2);
//     adsB2repeat_target_arr(adsB_target_arr, repeat_target_arr_3);

//     // 获取1和2的融合结果
//     std::vector<TargetRepeatAlg::RepeatResultDouble> current_result12;
//     fusiontype_param_.fusiontype = RadarAndGroundRadar;
//     target_repeat_alg_.get_repeat_result(repeat_target_arr_1, repeat_target_arr_2, fusiontype_param_, current_result12);

//     //解析匹配对得到1和2融合之后的结果
//     get_12_fusion_arr(repeat_target_arr_1, repeat_target_arr_2, current_result12, repeat_target_arr_1and2);

//     // 将12融合（repeat_mult_result_12）转换成多个融合的结构体
//     std::vector<TargetRepeatAlg::RepeatResultMult> cluster_tmp_123;

//     // 获取1和2融合之后的结果和3的融合
//     fusiontype_param_.fusiontype = RGRA;
//     std::vector<TargetRepeatAlg::RepeatResultDouble> current_result_12to3;
//     // 获取去重结果
//     target_repeat_alg_.get_repeat_result(repeat_target_arr_1and2, repeat_target_arr_3, fusiontype_param_, current_result_12to3);
//     // 12和3的融合 应该直接12和3且并集
//     cluster_tmp_123 = target_repeat_alg_.cluster_add(current_result12, current_result_12to3,
//                                                     repeat_target_arr_1, repeat_target_arr_2, repeat_target_arr_3);
//     // // 融合之后更新历史缓存
//     repeat_mult_result_ = target_repeat_alg_.update_history_cluster_result(cluster_tmp_123, repeat_mult_result_);

// }

// 方案1
void FusionMultiAlgKuerle::remove_repeat_RadarAndGroundRadarAndAdsB(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr,
                                                                    const std::vector<GroundRadarTarget> &ground_radar_repeat_target_arr,
                                                                    const std::vector<AdsBPositioningTarget> &adsB_target_arr,
                                                                    std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_mult_result_)
{
    // 结构体转换
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_1;
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_2;
    std::vector<TargetRepeatAlg::TargetRepeatInfoArr> repeat_target_arr_3;
    la_radar2repeat_target_arr(low_altitude_radar_target_arr, repeat_target_arr_1);
    ground_radar2repeat_target_arr(ground_radar_repeat_target_arr, repeat_target_arr_2);
    adsB2repeat_target_arr(adsB_target_arr, repeat_target_arr_3);

    // 获取1和3的融合结果
    std::vector<TargetRepeatAlg::RepeatResultDouble> current_result13;
    fusiontype_param_.fusiontype = RadarAndAdsB;
    target_repeat_alg_.get_repeat_result(repeat_target_arr_1, repeat_target_arr_3, fusiontype_param_, current_result13);

    target_repeat_alg_.second_check(repeat_mult_result_, current_result13);

    // 获取2和3的融合结果
    std::vector<TargetRepeatAlg::RepeatResultDouble> current_result23;
    fusiontype_param_.fusiontype = GroundRadarAdsB;
    target_repeat_alg_.get_repeat_result(repeat_target_arr_2, repeat_target_arr_3, fusiontype_param_, current_result23);

    target_repeat_alg_.second_check(repeat_mult_result_, current_result23);

    // 13和23的融合
    std::vector<TargetRepeatAlg::RepeatResultMult> cluster_tmp_123 = target_repeat_alg_.cluster_add(current_result13, current_result23,
                                                    repeat_target_arr_1, repeat_target_arr_2, repeat_target_arr_3);

    // // 融合之后更新历史缓存
    check_repeat_result(cluster_tmp_123);
    check_repeat_result(repeat_mult_result_);
    repeat_mult_result_ = target_repeat_alg_.update_history_cluster_result(cluster_tmp_123, repeat_mult_result_);
    check_repeat_result(repeat_mult_result_);
}

/**
 * @brief la_radar2repeat_target_arr - 低空雷达目标转换为去重算法输入格式
 * @param low_altitude_radar_target_arr - input, 低空雷达目标数组
 * @param repeat_target_arr - output, 转换后的去重算法输入数组
 * @details
 * 1. 遍历低空雷达目标数组
 * 2. 将LowAltitudeRadarTarget类型转换为TargetRepeatInfoArr类型
 * 3. 从目标的轨迹缓存中提取数据进行转换
 * 4. 复制目标ID、置信度和轨迹数据
 */
void FusionMultiAlgKuerle::la_radar2repeat_target_arr(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr,
                                                      std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr)
{
    // 清空输出数组
    repeat_target_arr.clear();

    // 遍历低空雷达目标数组
    for (const auto &la_radar_target : low_altitude_radar_target_arr)
    {
        // 检查目标是否有有效数据
        if (la_radar_target.target_catch.empty())
        {
            LogError("Low altitude radar target %s has no trajectory data, skip", la_radar_target.target_ID);
            continue;
        }

        // 创建TargetRepeatInfoArr对象
        TargetRepeatAlg::TargetRepeatInfoArr repeat_info_arr(la_radar_target.target_catch.capacity());

        // 复制目标ID和置信度
        memcpy(repeat_info_arr.target_ID, la_radar_target.target_ID, sizeof(repeat_info_arr.target_ID));
        repeat_info_arr.target_ID[sizeof(repeat_info_arr.target_ID) - 1] = '\0';
        repeat_info_arr.confidence = la_radar_target.confidence_level;
        repeat_info_arr.fusiontype = Radar;
        repeat_info_arr.first_apperance_time = la_radar_target.start_time;

        // 从CircularBuffer中提取所有轨迹数据并转换为TargetRepeatInfo
        size_t trajectory_size = la_radar_target.target_catch.size();
        repeat_info_arr.target_repeat_info_arr.clear();

        for (size_t i = 0; i < trajectory_size; ++i)
        {
            RadarTargetInfo radar_info = la_radar_target.target_catch[i];

            // 创建TargetRepeatInfo并填充数据
            TargetRepeatAlg::TargetRepeatInfo repeat_info;

            // 复制目标ID
            memcpy(repeat_info.target_ID, radar_info.radar_uav_ID, sizeof(repeat_info.target_ID));

            // 转换坐标和其他信息
            repeat_info.target_longitude = radar_info.radar_uav_longitude;
            repeat_info.target_latitude = radar_info.radar_uav_latitude;
            repeat_info.target_altitude = radar_info.radar_uav_altitude;
            repeat_info.target_timestamp = radar_info.uav_timestamp;
            repeat_info.target_course = radar_info.radar_uav_course;
            repeat_info.target_speed = std::abs(radar_info.radar_uav_speed);
            repeat_info.confidence = la_radar_target.confidence_level;

            repeat_info_arr.target_repeat_info_arr.push_back(repeat_info);
        }

        // 添加到输出数组
        repeat_target_arr.push_back(repeat_info_arr);
    }
}

/**
 * @brief ground_radar2repeat_target_arr - 地面雷达目标转换为去重算法输入格式
 * @param ground_radar_target_arr - input, 地面雷达目标数组
 * @param repeat_target_arr - output, 转换后的去重算法输入数组
 * @details
 * 1. 遍历地面雷达目标数组
 * 2. 将GroundRadarTarget类型转换为TargetRepeatInfoArr类型
 * 3. 从目标的轨迹缓存中提取数据进行转换
 * 4. 复制目标ID、置信度和轨迹数据
 */
void FusionMultiAlgKuerle::ground_radar2repeat_target_arr(const std::vector<GroundRadarTarget> &ground_radar_target_arr,
                                                          std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr)
{
    // 清空输出数组
    repeat_target_arr.clear();

    // 遍历地面雷达目标数组
    for (const auto &ground_radar_target : ground_radar_target_arr)
    {
        // 检查目标是否有有效数据
        if (ground_radar_target.target_catch.empty())
        {
            LogError("Ground radar target %s has no trajectory data, skip", ground_radar_target.target_ID);
            continue;
        }

        // 创建TargetRepeatInfoArr对象
        TargetRepeatAlg::TargetRepeatInfoArr repeat_info_arr(ground_radar_target.target_catch.capacity());

        // 复制目标ID和置信度
        memcpy(repeat_info_arr.target_ID, ground_radar_target.target_ID, sizeof(repeat_info_arr.target_ID));
        repeat_info_arr.confidence = ground_radar_target.confidence_level;
        repeat_info_arr.fusiontype = GroundRadar;
        repeat_info_arr.first_apperance_time = ground_radar_target.start_time;

        // 从CircularBuffer中提取所有轨迹数据并转换为TargetRepeatInfo
        size_t trajectory_size = ground_radar_target.target_catch.size();
        repeat_info_arr.target_repeat_info_arr.clear();

        for (size_t i = 0; i < trajectory_size; ++i)
        {
            RadarTargetInfo radar_info = ground_radar_target.target_catch[i];

            // 创建TargetRepeatInfo并填充数据
            TargetRepeatAlg::TargetRepeatInfo repeat_info;

            // 复制目标ID
            memcpy(repeat_info.target_ID, radar_info.radar_uav_ID, sizeof(repeat_info.target_ID));

            // 转换坐标和其他信息
            repeat_info.target_longitude = radar_info.radar_uav_longitude;
            repeat_info.target_latitude = radar_info.radar_uav_latitude;
            repeat_info.target_altitude = radar_info.radar_uav_altitude;
            repeat_info.target_timestamp = radar_info.uav_timestamp;
            repeat_info.target_course = radar_info.radar_uav_course;
            repeat_info.target_speed = std::abs(radar_info.radar_uav_speed);
            repeat_info.confidence = ground_radar_target.confidence_level;

            repeat_info_arr.target_repeat_info_arr.push_back(repeat_info);
        }

        // 添加到输出数组
        repeat_target_arr.push_back(repeat_info_arr);
    }
}

/**
 * @brief adsB2repeat_target_arr - ADS-B目标转换为去重算法输入格式
 * @param adsb_target_arr - input, ADS-B目标数组
 * @param repeat_target_arr - output, 转换后的去重算法输入数组
 *
 * 功能简介：将ADS-B目标数据转换为去重算法所需的输入格式
 *
 * 详细流程：
 * 1. 遍历ADS-B目标数组，检查每个目标是否有有效的轨迹数据
 * 2. 为每个有效目标创建TargetRepeatInfoArr对象
 * 3. 复制目标ID和置信度信息
 * 4. 从CircularBuffer中提取所有轨迹数据并转换为TargetRepeatInfo格式
 * 5. 将转换后的数据添加到输出数组中
 */
void FusionMultiAlgKuerle::adsB2repeat_target_arr(const std::vector<AdsBPositioningTarget> &adsb_target_arr,
                                                  std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr)
{
    // 清空输出数组
    repeat_target_arr.clear();

    // 遍历ADS-B目标数组
    for (const auto &adsb_target : adsb_target_arr)
    {
        // 检查目标是否有有效数据
        if (adsb_target.target_catch.empty())
        {
            LogError("ADS-B target {} has no trajectory data, skip", adsb_target.target_ID);
            continue;
        }

        // 创建TargetRepeatInfoArr对象
        TargetRepeatAlg::TargetRepeatInfoArr repeat_info_arr(adsb_target.target_catch.capacity());

        // 复制目标ID和置信度
        memcpy(repeat_info_arr.target_ID, adsb_target.target_ID, sizeof(repeat_info_arr.target_ID));
        repeat_info_arr.target_ID[sizeof(repeat_info_arr.target_ID) - 1] = '\0';
        repeat_info_arr.confidence = adsb_target.confidence_level;
        repeat_info_arr.fusiontype = AdsB;
        repeat_info_arr.first_apperance_time = adsb_target.start_time;

        // 从CircularBuffer中提取所有轨迹数据并转换为TargetRepeatInfo
        size_t trajectory_size = adsb_target.target_catch.size();
        repeat_info_arr.target_repeat_info_arr.clear();

        for (size_t i = 0; i < trajectory_size; ++i)
        {
            AdsBPositioningTargetInfo adsb_info = adsb_target.target_catch[i];

            // 创建TargetRepeatInfo并填充数据
            TargetRepeatAlg::TargetRepeatInfo repeat_info;

            // 复制目标ID
            memcpy(repeat_info.target_ID, adsb_info.targetID, sizeof(repeat_info.target_ID));
            repeat_info.target_ID[sizeof(repeat_info.target_ID) - 1] = '\0';

            // 转换坐标和其他信息
            repeat_info.target_longitude = static_cast<float>(adsb_info.cvl_longitude);
            repeat_info.target_latitude = static_cast<float>(adsb_info.cvl_latitude);
            repeat_info.target_altitude = adsb_info.cvl_altitude;
            repeat_info.target_timestamp = adsb_info.cvl_timestamp;
            repeat_info.target_course = adsb_info.cvl_course;
            repeat_info.target_speed = std::abs(adsb_info.cvl_speed);
            repeat_info.confidence = adsb_target.confidence_level;

            repeat_info_arr.target_repeat_info_arr.push_back(repeat_info);
        }

        // 添加到输出数组
        repeat_target_arr.push_back(repeat_info_arr);
    }
}

/**
 * @brief get_12_fusion_arr - 解析匹配对得到1和2融合之后的结果
 * @param repeat_target_arr_1 - input, 传感器1的目标数组
 * @param repeat_target_arr_2 - input, 传感器2的目标数组
 * @param repeat_mult_result_12 - input, 1和2的去重结果
 * @param repeat_target_arr_1and2 - output, 1和2融合之后的目标数组
 *
 * 功能简介：根据去重结果将两个传感器的目标数据融合成一个统一的目标数组
 *
 * 详细流程：
 * 1. 遍历去重结果数组，获取每个匹配对的目标ID信息
 * 2. 根据目标ID在两个传感器数组中查找对应的目标数据
 * 3. 比较两个目标的start_time，选择时间更早的目标作为基础
 * 4. 创建融合后的目标，合并两个传感器的轨迹数据
 * 5. 将融合后的目标添加到输出数组中
 */
void FusionMultiAlgKuerle::get_12_fusion_arr(const std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr_1,
                                             const std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr_2,
                                             const std::vector<TargetRepeatAlg::RepeatResultDouble> &repeat_mult_result_12,
                                             std::vector<TargetRepeatAlg::TargetRepeatInfoArr> &repeat_target_arr_1and2)
{
    // 清空输出数组
    repeat_target_arr_1and2.clear();

    // 遍历去重结果
    for (const auto &repeat_result : repeat_mult_result_12)
    {
        // 获取融合的id
        std::string target_ID1 = repeat_result.target_ID1;
        FusionType fusiontype1 = repeat_result.fusiontype1;
        std::string target_ID2 = repeat_result.target_ID2;
        FusionType fusiontype2 = repeat_result.fusiontype2;

        // 判断ID是否为空
        bool id1_empty = (target_ID1.empty() || target_ID1[0] == '\0');
        bool id2_empty = (target_ID2.empty() || target_ID2[0] == '\0');

        TargetRepeatAlg::TargetRepeatInfoArr target_repeat_info_arr_find;
        bool ret1, ret2;
        if (id1_empty && !id2_empty)
        {
            // 只有目标2
            ret1 = target_repeat_alg_.find_arr_from_targetID_fusion_type(repeat_target_arr_1, target_ID2, fusiontype2, target_repeat_info_arr_find);
            ret2 = target_repeat_alg_.find_arr_from_targetID_fusion_type(repeat_target_arr_2, target_ID2, fusiontype2, target_repeat_info_arr_find);
            // 添加到输出数组
            if (ret1 || ret2)
            {
                repeat_target_arr_1and2.push_back(target_repeat_info_arr_find);
            }
            else
            {
                LogError("Can not find target {} in repeat_target_arr_2", target_ID2);
            }
            continue;
        }
        else if (!id1_empty && id2_empty)
        {
            // 只有目标1
            ret1 = target_repeat_alg_.find_arr_from_targetID_fusion_type(repeat_target_arr_1, target_ID1, fusiontype1, target_repeat_info_arr_find);
            ret2 = target_repeat_alg_.find_arr_from_targetID_fusion_type(repeat_target_arr_2, target_ID1, fusiontype1, target_repeat_info_arr_find);
            // 添加到输出数组
            if (ret1 || ret2)
            {
                repeat_target_arr_1and2.push_back(target_repeat_info_arr_find);
            }
            else
            {
                LogError("Can not find target {} in repeat_target_arr_1", target_ID1);
            }
            continue;
        }
        else if (!id1_empty && !id2_empty)
        {
            // 两个目标都有 默认保留第一个
            ret1 = target_repeat_alg_.find_arr_from_targetID_fusion_type(repeat_target_arr_1, target_ID1, fusiontype1, target_repeat_info_arr_find);
            ret2 = target_repeat_alg_.find_arr_from_targetID_fusion_type(repeat_target_arr_2, target_ID1, fusiontype1, target_repeat_info_arr_find);
            // 添加到输出数组
            if (ret1 || ret2)
            {
                repeat_target_arr_1and2.push_back(target_repeat_info_arr_find);
            }
            else
            {
                LogError("Can not find target {}", target_ID1);
            }
            continue;
        }
        else
        {
            LogError("target_ID1 and target_ID2 are both null {}", 0);
            continue;
        }
    }
}

/**
 * @brief 将算法类中的MultiSensorTargetInput输出到FusionTargetInfoOutput（输出给平台的数据）
 * @param  MultiSensorTargetInput - input,         输入数据
 * @param  repeat_result - input,                  缓存的融合ID对
 * @param  repeat_result_gr2gr - input,            缓存的地面雷达去重ID对
 * @param  FusionTargetInfoOutput -output , 融合输出数据
 * @return 成功：0， 失败： 非0
 * @details 将算法类中的mult_fusion_result_arr_输出到FusionTargetInfoOutput（输出给平台的数据）,需要根据fusion_result从原始缓存中补充其他属性
 * 1、从MultiSensorTargetInput中找到输入数据的目标ID（ID+DeviceID或ID）；
 * 2、根据输入目标的ID从repeat_result中找到融合目标；
 * 3、将融合目标赋值到融合目标当中；
 */
int FusionMultiAlgKuerle::output_fusion_result(MultiSensorTargetInputInfoArr *MultiSensorTargetInput,
                                               const std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_result,
                                               const std::vector<TargetRepeatAlg::RepeatResultDouble> &repeat_result_gr2gr,
                                               FusionTargetInfoArr *FusionTargetInfoOutput)
{
    FusionTargetInfoOutput->uavNum = 0;
    std::string input_target_ID = "";
    FusionType input_target_type = FusionType::TDOA;
    check_fusion_result();
    if (MultiSensorTargetInput->RadarTargetNum > 0)
    {
        input_target_ID = std::string(MultiSensorTargetInput->RadarTargetInfoArr[0].radar_uav_ID) + "#" +
                          std::string(MultiSensorTargetInput->RadarTargetInfoArr[0].radar_device_ID);
        input_target_type = FusionType::Radar;
    }
    else if (MultiSensorTargetInput->GRadarTargetNum > 0)
    {
        input_target_ID = std::string(MultiSensorTargetInput->GRadarTargetInfoArr[0].radar_uav_ID) + "#" +
                          std::string(MultiSensorTargetInput->GRadarTargetInfoArr[0].radar_device_ID);
        input_target_type = FusionType::GroundRadar;
    }
    else if (MultiSensorTargetInput->AdsBPositioningTargetNum > 0)
    {
        input_target_ID = std::string(MultiSensorTargetInput->AdsBPositioningTargetInfoArr[0].targetID) + "#" +
                          std::string(MultiSensorTargetInput->AdsBPositioningTargetInfoArr[0].adsB_device_ID);
        input_target_type = FusionType::AdsB;
    }
    else
    {
    }
    if (input_target_type == FusionType::TDOA)
    {
        LogWarn("MultiSensorTargetInput is not Radar or RroundRadar or AdsB, RadarTargetNum {} GRadarTargetNum {} AdsBPositioningTargetNum {}",
                MultiSensorTargetInput->RadarTargetNum,
                MultiSensorTargetInput->GRadarTargetNum,
                MultiSensorTargetInput->AdsBPositioningTargetNum);
    }
    else
    {
        TargetRepeatAlg::RepeatResultMult match_repeat_result = {}; // 默认初始化
        // 使用引用避免拷贝
        for (const auto &r_result : repeat_result) // 注意：使用 const 引用
        {
            // 使用安全字符串比较函数，自动处理无效字符串
            bool llr_is_same = safe_string_compare(r_result.target_ID_llr, input_target_ID.c_str());
            bool gr_is_same = safe_string_compare(r_result.target_ID_gr, input_target_ID.c_str());
            bool adsB_is_same = safe_string_compare(r_result.target_ID_adsB, input_target_ID.c_str());
            if (llr_is_same || gr_is_same || adsB_is_same)
            {
                match_repeat_result = r_result;
                break;
            }
        }
        if (match_repeat_result.start_time == 0)
        {
            // 如果从三元组中找不到，则从地面雷达和地面雷达的二元组中查找对应id
            // 从repeat_result_gr2gr中获取保留的id
            for (const auto &r_result : repeat_result_gr2gr)
            {
                // 使用安全字符串比较函数，自动处理无效字符串
                bool gr1_is_same = safe_string_compare(r_result.target_ID1, input_target_ID.c_str());
                bool gr2_is_same = safe_string_compare(r_result.target_ID2, input_target_ID.c_str());
                if (gr1_is_same || gr2_is_same)
                {
                    input_target_ID = std::string(r_result.first_apperance_ID);
                    // 从三元组中查找并且赋值
                    for (const auto &r_result : repeat_result) // 注意：使用 const 引用
                    {
                        // 使用安全字符串比较函数，自动处理无效字符串
                        bool llr_is_same = safe_string_compare(r_result.target_ID_llr, input_target_ID.c_str());
                        bool gr_is_same = safe_string_compare(r_result.target_ID_gr, input_target_ID.c_str());
                        bool adsB_is_same = safe_string_compare(r_result.target_ID_adsB, input_target_ID.c_str());
                        if (llr_is_same || gr_is_same || adsB_is_same)
                        {
                            match_repeat_result = r_result;
                            break;
                        }
                    }
                    break;
                }
            } 
        }
        if (match_repeat_result.start_time > 0)
        {
            FusionTargetInfoOutput->FusionTargetResultArr[0] = match_repeat_result.fusion_target_output;
            FusionTargetInfoOutput->uavNum = 1;
        }
        else
        {       
            // LogError("MultiSensorTargetInput is Found in repeat_result buffer, Radar or RroundRadar or AdsB, RadarTargetNum {} GRadarTargetNum {} AdsBPositioningTargetNum {}",
            //         MultiSensorTargetInput->RadarTargetNum,
            //         MultiSensorTargetInput->GRadarTargetNum,
            //         MultiSensorTargetInput->AdsBPositioningTargetNum);
            test_fusion_data(MultiSensorTargetInput->GRadarTargetInfoArr[0],
                             MultiSensorTargetInput->RadarTargetInfoArr[0],
                             MultiSensorTargetInput->AdsBPositioningTargetInfoArr[0],
                             input_target_type,
                             FusionTargetInfoOutput->FusionTargetResultArr[0]);
            FusionTargetInfoOutput->uavNum = 1;
        }
    }
    return 0;
}


/**
 * @brief 将输入数据直接输出到FusionTargetInfoOutput（输出给平台的数据）
 * @param  MultiSensorTargetInput - input,         输入数据
 * @param  FusionTargetInfoOutput -output , 融合输出数据
 * @return 成功：0， 失败： 非0
 * @details 将输入数据直接输出到FusionTargetInfoOutput（输出给平台的数据）
 */
int FusionMultiAlgKuerle::output_fusion_result_from_input(const MultiSensorTargetInputInfoArr *MultiSensorTargetInput,
                                                           FusionTargetInfoArr *FusionTargetInfoOutput)
{
    FusionTargetInfo fusion_target;
    // 根据输入数据的类型，直接输出
    if(MultiSensorTargetInput->GRadarTargetNum > 0)
    {
        const RadarTargetInfo &gradar_target = MultiSensorTargetInput->GRadarTargetInfoArr[0];
        memcpy(fusion_target.targetID, gradar_target.radar_uav_ID, sizeof(fusion_target.targetID));
        fusion_target.target_state = FusionType::GroundRadar;
        fusion_target.uav_longitude = gradar_target.radar_uav_longitude;
        fusion_target.uav_latitude = gradar_target.radar_uav_latitude;
        fusion_target.uav_altitude = gradar_target.radar_uav_altitude;
        fusion_target.speed = gradar_target.radar_uav_speed;
        fusion_target.course = gradar_target.radar_uav_course;
        fusion_target.uav_timestamp = gradar_target.uav_timestamp;
        fusion_target.confidence_level = 100.0f;
    }
    else if(MultiSensorTargetInput->RadarTargetNum > 0)
    {
        const RadarTargetInfo &lowradar_target = MultiSensorTargetInput->RadarTargetInfoArr[0];
        memcpy(fusion_target.targetID, lowradar_target.radar_uav_ID, sizeof(fusion_target.targetID));
        fusion_target.target_state = FusionType::Radar;
        fusion_target.uav_longitude = lowradar_target.radar_uav_longitude;
        fusion_target.uav_latitude = lowradar_target.radar_uav_latitude;
        fusion_target.uav_altitude = lowradar_target.radar_uav_altitude;
        fusion_target.speed = lowradar_target.radar_uav_speed;
        fusion_target.course = lowradar_target.radar_uav_course;
        fusion_target.uav_timestamp = lowradar_target.uav_timestamp;
        fusion_target.confidence_level = 100.0f;
    }
    else if(MultiSensorTargetInput->AdsBPositioningTargetNum > 0)
    {
        const AdsBPositioningTargetInfo &adsB_target = MultiSensorTargetInput->AdsBPositioningTargetInfoArr[0];
        memcpy(fusion_target.targetID, adsB_target.targetID, sizeof(fusion_target.targetID));
        fusion_target.target_state = FusionType::AdsB;
        fusion_target.uav_longitude = adsB_target.cvl_longitude;
        fusion_target.uav_latitude = adsB_target.cvl_latitude;
        fusion_target.uav_altitude = adsB_target.cvl_altitude;
        fusion_target.speed = adsB_target.cvl_speed;
        fusion_target.course = adsB_target.cvl_course;
        fusion_target.uav_timestamp = adsB_target.cvl_timestamp;
        fusion_target.confidence_level = 200.0f;
    }
    else
    {

    }
    FusionTargetInfoOutput->FusionTargetResultArr[0] = fusion_target;
    FusionTargetInfoOutput->uavNum = 1;
}

/**
* @brief test_fusion_data- 融合数据的测试
* @param gradar_target - input, 地面雷达目标
* @param lowradar_target - input, 低空雷达目标
* @param adsB_target - input, adsB目标
* @param fusion_type - input, 融合类型
* @param fusion_target - output, 融合目标
* @details
* 1. 不进行任何融合处理，根据融合类型输出融合目标；
*/
void FusionMultiAlgKuerle::test_fusion_data(const RadarTargetInfo &gradar_target,
                                            const RadarTargetInfo &lowradar_target,
                                            const AdsBPositioningTargetInfo &adsB_target,
                                            const FusionType &fusion_type,
                                            FusionTargetInfo &fusion_target)
{
    if(fusion_type == FusionType::GroundRadar)
    {
        memcpy(fusion_target.targetID, gradar_target.radar_uav_ID, sizeof(fusion_target.targetID));
        fusion_target.target_state = FusionType::GroundRadar;
        fusion_target.uav_longitude = gradar_target.radar_uav_longitude;
        fusion_target.uav_latitude = gradar_target.radar_uav_latitude;
        fusion_target.uav_altitude = gradar_target.radar_uav_altitude;
        fusion_target.speed = gradar_target.radar_uav_speed;
        fusion_target.course = gradar_target.radar_uav_course;
        fusion_target.uav_timestamp = gradar_target.uav_timestamp;
        fusion_target.confidence_level = 100.0f;
    }
    else if(fusion_type == FusionType::Radar)
    {
        memcpy(fusion_target.targetID, lowradar_target.radar_uav_ID, sizeof(fusion_target.targetID));
        fusion_target.target_state = FusionType::Radar;
        fusion_target.uav_longitude = lowradar_target.radar_uav_longitude;
        fusion_target.uav_latitude = lowradar_target.radar_uav_latitude;
        fusion_target.uav_altitude = lowradar_target.radar_uav_altitude;
        fusion_target.speed = lowradar_target.radar_uav_speed;
        fusion_target.course = lowradar_target.radar_uav_course;
        fusion_target.uav_timestamp = lowradar_target.uav_timestamp;
        fusion_target.confidence_level = 100.0f;
    }
    else if(fusion_type == FusionType::AdsB)
    {
        memcpy(fusion_target.targetID, adsB_target.targetID, sizeof(fusion_target.targetID));
        fusion_target.target_state = FusionType::AdsB;
        fusion_target.uav_longitude = adsB_target.cvl_longitude;
        fusion_target.uav_latitude = adsB_target.cvl_latitude;
        fusion_target.uav_altitude = adsB_target.cvl_altitude;
        fusion_target.speed = adsB_target.cvl_speed;
        fusion_target.course = adsB_target.cvl_course;
        fusion_target.uav_timestamp = adsB_target.cvl_timestamp;
        fusion_target.confidence_level = 200.0f;
    }
    else
    {

    }
}

/*
* @brief is_radar_target_fragmented - 判断地面雷达目标中的是否为分裂目标
* @param gradar_target - input, 待判断的地面雷达目标
* @param ground_radar_map - input, 地面雷达目标的字典
* @param repeat_result - input, 三种传感器的匹配对
* @param distance_thre - input, 过滤的距离阈值
* @param speed_dis_thre - input, 过滤的速度距离阈值
* @param bool true 为分裂目标， false 不上分裂目标
* @details
* 1. 先从repeat_result这个容器中判断是否存在地面雷达目标与ads-b的融合id对，如果有进行下一步；
* 2. 判断该地面雷达目标id与融合上的地面雷达id是否在距离和速度距离上满足条件，满足则是分裂的目标，否则不上分裂的目标；
* */
bool FusionMultiAlgKuerle::is_radar_target_fragmented(const TargetRepeatAlg::RepeatResultMult &gradar_target,
                                const std::unordered_map<std::string, GroundRadarTarget*> &ground_radar_map,
                                const std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_result,
                                const double &distance_thre,
                                const double &speed_dis_thre)
{
    bool is_fragmented = false;
    if(safe_string_compare(gradar_target.target_ID_gr, gradar_target.first_apperance_ID) && is_string_valid(gradar_target.first_apperance_ID))
    {
        std::vector<TargetRepeatAlg::RepeatResultMult> gradar_adsb_repeat;
        for(auto r_result : repeat_result)
        {
            if(r_result.target_ID_gr[0] != '\0' && r_result.target_ID_adsB[0] != '\0')
            {
                gradar_adsb_repeat.push_back(r_result);
            }
        }

        if(gradar_adsb_repeat.size() > 0)
        {
            for(auto g_result : gradar_adsb_repeat)
            {
                if (auto it = ground_radar_map.find(std::string(g_result.target_ID_gr)); it != ground_radar_map.end())
                {
                    if(safe_string_compare(g_result.target_ID_gr, gradar_target.target_ID_gr) && is_string_valid(gradar_target.target_ID_gr))
                    {
                        continue;
                    }
                    else
                    {
                        //判断两地面雷达的速度和位置是否在允许的范围内
                        double distance = distanceEarth(it->second->target_catch.get_newest().radar_uav_latitude,
                                                        it->second->target_catch.get_newest().radar_uav_longitude,
                                                        gradar_target.fusion_target_output.uav_latitude,
                                                        gradar_target.fusion_target_output.uav_longitude);
                        double speed_dis = speed_dis_thre;
                        if(fabs(it->second->target_catch.get_newest().radar_uav_speed) > FRAGMENTED_LOW_SPEED
                        && fabs(gradar_target.fusion_target_output.speed) > FRAGMENTED_LOW_SPEED)
                        {
                            speed_dis = fabs(fabs(it->second->target_catch.get_newest().radar_uav_speed) - fabs(gradar_target.fusion_target_output.speed));
                        }
                        // 判断置信度是否符合条件(分裂目标的置信度)
                        // if(distance < distance_thre && speed_dis <= speed_dis_thre && gradar_target.fusion_target_output.confidence_level <= 30)
                        if(distance < distance_thre && speed_dis <= speed_dis_thre)
                        {
                            is_fragmented = true;
                            LogDebug("is_fragmented check success gradar_target id:{} is is_fragmented, due to g_adsb_target: gid:{}<->adsb:{} distance {} < distance_thre {} && speed_dis {} <= speed_dis_thre {} && conf {}",
                                     gradar_target.target_ID_gr, g_result.target_ID_gr, g_result.target_ID_adsB, distance, distance_thre, speed_dis, speed_dis_thre, gradar_target.fusion_target_output.confidence_level);
                            break;
                        }
                        else
                        {
                            LogDebug("is_fragmented check failed gradar_target id:{} is not is_fragmented, due to g_adsb_target: gid:{}<->adsb:{} distance {} < distance_thre {} && speed_dis {} <= speed_dis_thre {} && conf {}",
                                     gradar_target.target_ID_gr, g_result.target_ID_gr, g_result.target_ID_adsB, distance, distance_thre, speed_dis, speed_dis_thre, gradar_target.fusion_target_output.confidence_level);
                        }
                    }
                }
                else
                {
                    LogError("target_ID_gr {} is not found, in ground_radar_map",
                             g_result.target_ID_gr);
                }
            }
        }
        else
        {
            is_fragmented = false;
        }
    }
    else
    {
        is_fragmented = false;
    }
    return is_fragmented;
}



/**
 * @brief 将算法类中的mult_fusion_result_arr_输出到FusionTargetInfoOutput（输出给平台的数据）
 * @param  low_altitude_radar_target -input , 低空雷达的原始缓存数据
 * @param  ground_radar_repeat_target -input , 地面雷达的原始缓存数据
 * @param  adsB_target_arr_target -input , ADS-B的原始缓存数据
 * @param  repeat_gr2gr_result -input , 地面雷达去重结果
 * @param  repeat_result -output , 融合输出数据
 * @return 无
 * @details 更新repeat_result中的fusion_target_output结果；
 */
void FusionMultiAlgKuerle::update_fusion_result(std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target,
                                                std::vector<GroundRadarTarget> &ground_radar_repeat_target,
                                                std::vector<GroundRadarTarget> &ground_radar_target,
                                                std::vector<AdsBPositioningTarget> &adsB_target_arr_target,
                                                std::vector<TargetRepeatAlg::RepeatResultDouble> &repeat_gr2gr_result,
                                                std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_result)
{

    // 预先创建ID到最新雷达目标的映射（使用指针避免值拷贝）
    std::unordered_map<std::string, LowAltitudeRadarTarget*> low_altitude_map;
    for (auto &ltarget : low_altitude_radar_target)
    {
        std::string target_ID_str = std::string(ltarget.target_ID);
        low_altitude_map[target_ID_str] = &ltarget;
    }

    std::unordered_map<std::string, GroundRadarTarget*> ground_radar_map;
    for (auto &gtarget : ground_radar_repeat_target)
    {
        std::string target_ID_str = std::string(gtarget.target_ID);
        ground_radar_map[target_ID_str] = &gtarget;
    }

    std::unordered_map<std::string, AdsBPositioningTarget*> adsB_target_map;
    for (auto &adstarget : adsB_target_arr_target)
    {
        std::string target_ID_str = std::string(adstarget.target_ID);
        adsB_target_map[target_ID_str] = &adstarget;
    }

    for (auto &fusion_result : repeat_result)
    {
        fusion_result.fusion_target_output = {};
        // 按照#切分，获得目标id和设备id
        std::string target_ID_str = std::string(fusion_result.first_apperance_ID);
        std::string target_ID = target_ID_str.substr(0, target_ID_str.find("#"));
        if(!is_string_empty(fusion_result.target_ID_adsB))
        {
            std::string AdsB_target_ID_str = std::string(fusion_result.target_ID_adsB);
            std::string AdsB_target_ID = AdsB_target_ID_str.substr(0, AdsB_target_ID_str.find("#"));
            // 使用安全字符串拷贝函数，避免缓冲区溢出
            if (!safe_string_copy_from_std(fusion_result.fusion_target_output.targetID, AdsB_target_ID, sizeof(fusion_result.fusion_target_output.targetID))) {
                LogError("ADS-B target ID too long, truncated: {}", AdsB_target_ID);
            }
            if (!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[5], AdsB_target_ID, sizeof(fusion_result.fusion_target_output.targetID))) {
                LogError("ID_before_fusion[4] too long, AdsB truncated: {}", AdsB_target_ID);
            }
        }
        else
        {
            // 目标ID赋值，使用安全字符串拷贝函数
            if (!safe_string_copy_from_std(fusion_result.fusion_target_output.targetID, target_ID, sizeof(fusion_result.fusion_target_output.targetID))) {
                LogError("Target ID too long, truncated: {}", target_ID);
            }
            if (!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[5], target_ID, sizeof(fusion_result.fusion_target_output.targetID))) {
                LogError("ID_before_fusion[4] too long, truncated: {}", target_ID);
            }
        }
        // ID_before_fusion 赋值 依次为  target_ID_llr  target_ID_gr  target_ID_adsB
        // 检查是否所有ID都为空或无效
        if((is_string_empty(fusion_result.target_ID_llr) &&
           is_string_empty(fusion_result.target_ID_gr) &&
           is_string_empty(fusion_result.target_ID_adsB)) || is_string_empty(fusion_result.first_apperance_ID))
        {
            LogError("repeat_result_update has three id are {}",0);
        }
        // 添加融合前的目标ID
        // 1. 多传感器去重的id
        std::string target_ID_llr_str = std::string(fusion_result.target_ID_llr);
        std::string target_ID_llr_str_ = target_ID_llr_str.substr(0, target_ID_llr_str.find("#"));

        std::string target_ID_gr_str = std::string(fusion_result.target_ID_gr);
        std::string target_ID_gr_str_ = target_ID_gr_str.substr(0, target_ID_gr_str.find("#"));

        std::string target_ID_adsB_str = std::string(fusion_result.target_ID_adsB);
        std::string target_ID_adsB_str_ = target_ID_adsB_str.substr(0, target_ID_adsB_str.find("#"));

        if(!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[0], target_ID_llr_str_, sizeof(fusion_result.fusion_target_output.ID_before_fusion[0])))
        {
            LogWarn("ID_before_fusion[0] too long, truncated: {}", target_ID_llr_str_);
        }
        if(!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[1], target_ID_gr_str_, sizeof(fusion_result.fusion_target_output.ID_before_fusion[1])))
        {
            LogError("ID_before_fusion[1] too long, truncated: {}", target_ID_gr_str_);
        }
        if(!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[2], target_ID_adsB_str_, sizeof(fusion_result.fusion_target_output.ID_before_fusion[2])))
        {
            LogError("ID_before_fusion[2] too long, truncated: {}", target_ID_adsB_str_);
        }
        // 2. 如果有地面雷达之间的去重，添加到后边
        for (const auto &gr2gr_result : repeat_gr2gr_result)
        {
            if (safe_string_compare(gr2gr_result.target_ID1, fusion_result.target_ID_gr) && is_string_valid(gr2gr_result.target_ID2))
            {
                std::string gr2gr_result_targetID2_str = std::string(gr2gr_result.target_ID2);
                std::string gr2gr_result_targetID2_str_ = gr2gr_result_targetID2_str.substr(0, gr2gr_result_targetID2_str.find("#"));

                if(!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[3], gr2gr_result_targetID2_str_, sizeof(fusion_result.fusion_target_output.ID_before_fusion[3])))
                {
                    LogError("ID_before_fusion[4] too long, gr2gr_result_targetID2_str_ truncated: {}", gr2gr_result_targetID2_str_);
                }
                fusion_result.fusion_target_output.target_state = FusionType::GGroundRadar;
            }
            else if (safe_string_compare(gr2gr_result.target_ID2, fusion_result.target_ID_gr) && is_string_valid(gr2gr_result.target_ID1))
            {
                std::string gr2gr_result_targetID1_str = std::string(gr2gr_result.target_ID1);
                std::string gr2gr_result_targetID1_str_ = gr2gr_result_targetID1_str.substr(0, gr2gr_result_targetID1_str.find("#"));
                if(!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[3], gr2gr_result_targetID1_str_, sizeof(fusion_result.fusion_target_output.ID_before_fusion[3])))
                {
                    LogError("ID_before_fusion[4] too long, gr2gr_result_targetID1_str_ truncated: {}", gr2gr_result_targetID1_str_);
                }
                fusion_result.fusion_target_output.target_state = FusionType::GGroundRadar;
            }
            else if(safe_string_compare(gr2gr_result.first_apperance_ID, fusion_result.target_ID_gr))
            {
                // 判断target_ID1和target_ID2存在几个,如果只有一个,则是GroundRadar,赋值给ID_before_fusion[3]
                bool is_target_ID1_valid = is_string_valid(gr2gr_result.target_ID1);
                bool is_target_ID2_valid = is_string_valid(gr2gr_result.target_ID2);
                if(is_target_ID1_valid)
                {
                    std::string gr2gr_result_targetID1_str = std::string(gr2gr_result.target_ID1);
                    std::string gr2gr_result_targetID1_str_ = gr2gr_result_targetID1_str.substr(0, gr2gr_result_targetID1_str.find("#"));
                    if(!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[3], gr2gr_result_targetID1_str_, sizeof(fusion_result.fusion_target_output.ID_before_fusion[3])))
                    {
                        LogError("ID_before_fusion[3] too long, gr2gr_result_targetID1_str_ truncated: {}", gr2gr_result_targetID1_str_);
                    }
                }
                if(is_target_ID2_valid)
                {
                    std::string gr2gr_result_targetID2_str = std::string(gr2gr_result.target_ID2);
                    std::string gr2gr_result_targetID2_str_ = gr2gr_result_targetID2_str.substr(0, gr2gr_result_targetID2_str.find("#"));
                    if(!safe_string_copy_from_std(fusion_result.fusion_target_output.ID_before_fusion[4], gr2gr_result_targetID2_str_, sizeof(fusion_result.fusion_target_output.ID_before_fusion[4])))
                    {
                        LogError("ID_before_fusion[3] too long, gr2gr_result_targetID2_str_ truncated: {}", gr2gr_result_targetID2_str_);
                    }
                }
                if(is_target_ID1_valid && is_target_ID2_valid)
                {
                    fusion_result.fusion_target_output.target_state = FusionType::GGroundRadar;
                }
            }

        }
        if (fusion_result.target_ID_llr[0] != '\0' && fusion_result.target_ID_gr[0] == '\0' && fusion_result.target_ID_adsB[0] == '\0')
        {
            fusion_result.fusion_target_output.target_state = FusionType::Radar;
            if (auto it = low_altitude_map.find(std::string(fusion_result.target_ID_llr)); it != low_altitude_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = it->second->target_catch.get_newest().uav_timestamp;
                fusion_result.fusion_target_output.confidence_level = it->second->confidence_level;
            }
            else
            {
                LogError("target_ID_llr {} not found", fusion_result.target_ID_llr);
            }
        }
        // 地面雷达目标（GGroundRadar || GroundRadar）
        else if (fusion_result.target_ID_llr[0] == '\0' && fusion_result.target_ID_gr[0] != '\0' && fusion_result.target_ID_adsB[0] == '\0')
        {
            if(fusion_result.fusion_target_output.target_state != FusionType::GGroundRadar)
            {
                fusion_result.fusion_target_output.target_state = FusionType::GroundRadar;
            }
            if (auto it = ground_radar_map.find(std::string(fusion_result.target_ID_gr)); it != ground_radar_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = it->second->target_catch.get_newest().uav_timestamp;
                #ifdef AlgTest
                fusion_result.fusion_target_output.confidence_level = it->second->confidence_level;
                #else
                // 最小值为0
                float confidence_level_ = 0.0f;
                confidence_level_ = std::max(it->second->confidence_level, confidence_level_);
                fusion_result.fusion_target_output.confidence_level = confidence_level_;
                #endif
                if(fusion_result.fusion_target_output.target_state == FusionType::GGroundRadar)
                {
                    // 融合目标的置信度设置为100
                    fusion_result.fusion_target_output.confidence_level = 100.0f;
                }
                // 纯地面雷达
                else
                {
                    //利用ads-b过滤民航地面雷达分裂的目标
                    if(is_radar_target_fragmented(fusion_result,
                                                  ground_radar_map,
                                                  repeat_result,
                                                  fusion_param_.fragmented_distance_thres,
                                                  fusion_param_.fragmented_speed_dis_thres))
                    {
                        // 输出结果的置信度设置为0
                        fusion_result.fusion_target_output.confidence_level = 0;
                        // 缓存中最新的数据也要设置为0
                        // 从原始的地面雷达缓存中查找，查找到将置信度设置为0
                        for (auto &target : ground_radar_target)
                        {
                            if (safe_string_compare(target.target_ID, fusion_result.target_ID_gr))
                            {
                                target.confidence_level = -30.0f;
                                LogDebug("target_ID_gr {} is fragmented, confidence_level = 0",
                                        fusion_result.target_ID_gr);
                                break;
                            }
                        }
                    }
                    // 
                }
            }
            else
            {
                LogError("target_ID_gr {} is not found, in low_altitude_radar_target",
                         fusion_result.target_ID_gr);
            }
        }
        else if (fusion_result.target_ID_llr[0] == '\0' && fusion_result.target_ID_gr[0] == '\0' && fusion_result.target_ID_adsB[0] != '\0')
        {
            fusion_result.fusion_target_output.target_state = FusionType::AdsB;
            if (auto it = adsB_target_map.find(std::string(fusion_result.target_ID_adsB)); it != adsB_target_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = it->second->target_catch.get_newest().cvl_longitude;
                fusion_result.fusion_target_output.uav_latitude = it->second->target_catch.get_newest().cvl_latitude;
                fusion_result.fusion_target_output.uav_altitude = it->second->target_catch.get_newest().cvl_altitude;
                fusion_result.fusion_target_output.speed = it->second->target_catch.get_newest().cvl_speed;
                fusion_result.fusion_target_output.course = it->second->target_catch.get_newest().cvl_course;
                fusion_result.fusion_target_output.uav_timestamp = it->second->target_catch.get_newest().cvl_timestamp;
                fusion_result.fusion_target_output.confidence_level = 200.0f;
            }
            else
            {
                LogError("target_ID_adsB {} is not found, in low_altitude_radar_target",
                         fusion_result.target_ID_adsB);
            }
        }
        else if (fusion_result.target_ID_llr[0] != '\0' && fusion_result.target_ID_gr[0] != '\0' && fusion_result.target_ID_adsB[0] == '\0')
        {
            fusion_result.fusion_target_output.target_state = FusionType::RadarAndGroundRadar;
            auto radar_it = low_altitude_map.find(std::string(fusion_result.target_ID_llr));
            auto gradar_it = ground_radar_map.find(std::string(fusion_result.target_ID_gr));
            if (gradar_it != ground_radar_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = gradar_it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = gradar_it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = gradar_it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = gradar_it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = gradar_it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = gradar_it->second->target_catch.get_newest().uav_timestamp;
                fusion_result.fusion_target_output.confidence_level = 100.0f;
                // 将缓存的数据的置信度赋值为0
                gradar_it->second->confidence_level = 0.0f;
                continue;
            }
            if (radar_it != low_altitude_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = radar_it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = radar_it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = radar_it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = radar_it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = radar_it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = radar_it->second->target_catch.get_newest().uav_timestamp;
                fusion_result.fusion_target_output.confidence_level = 100.0f;
                // 将缓存的数据的置信度赋值为0
                radar_it->second->confidence_level = 0.0f;
                continue;
            }
            LogError("target_ID_llr {} and target_ID_adsB {} is not found, in low_altitude_radar_target and adsB_target_map",
                     fusion_result.target_ID_llr, fusion_result.target_ID_adsB);
        }
        else if (fusion_result.target_ID_llr[0] != '\0' && fusion_result.target_ID_gr[0] == '\0' && fusion_result.target_ID_adsB[0] != '\0')
        {
            fusion_result.fusion_target_output.target_state = FusionType::RadarAndAdsB;
            auto radar_it = low_altitude_map.find(std::string(fusion_result.target_ID_llr));
            auto absd_it = adsB_target_map.find(std::string(fusion_result.target_ID_adsB));
            if (absd_it != adsB_target_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = absd_it->second->target_catch.get_newest().cvl_longitude;
                fusion_result.fusion_target_output.uav_latitude = absd_it->second->target_catch.get_newest().cvl_latitude;
                fusion_result.fusion_target_output.uav_altitude = absd_it->second->target_catch.get_newest().cvl_altitude;
                fusion_result.fusion_target_output.speed = absd_it->second->target_catch.get_newest().cvl_speed;
                fusion_result.fusion_target_output.course = absd_it->second->target_catch.get_newest().cvl_course;
                fusion_result.fusion_target_output.uav_timestamp = absd_it->second->target_catch.get_newest().cvl_timestamp;
                fusion_result.fusion_target_output.confidence_level = 200.0f;
                // 将缓存的数据的置信度赋值为0
                absd_it->second->confidence_level = 0.0f;
                continue;
            }
            if (radar_it != low_altitude_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = radar_it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = radar_it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = radar_it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = radar_it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = radar_it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = radar_it->second->target_catch.get_newest().uav_timestamp;
                fusion_result.fusion_target_output.confidence_level = 200.0f;
                // 将缓存的数据的置信度赋值为0
                radar_it->second->confidence_level = 0.0f;
                continue;
            }
            LogError("target_ID_llr {} and target_ID_adsB {} is not found, in low_altitude_radar_target and adsB_target_map",
                     fusion_result.target_ID_llr, fusion_result.target_ID_adsB);
        }
        else if (fusion_result.target_ID_llr[0] == '\0' && fusion_result.target_ID_gr[0] != '\0' && fusion_result.target_ID_adsB[0] != '\0')
        {
            fusion_result.fusion_target_output.target_state = FusionType::GroundRadarAdsB;
            auto gradar_it = ground_radar_map.find(std::string(fusion_result.target_ID_gr));
            auto absb_it = adsB_target_map.find(std::string(fusion_result.target_ID_adsB));
            if (absb_it != adsB_target_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = absb_it->second->target_catch.get_newest().cvl_longitude;
                fusion_result.fusion_target_output.uav_latitude = absb_it->second->target_catch.get_newest().cvl_latitude;
                fusion_result.fusion_target_output.uav_altitude = absb_it->second->target_catch.get_newest().cvl_altitude;
                fusion_result.fusion_target_output.speed = absb_it->second->target_catch.get_newest().cvl_speed;
                fusion_result.fusion_target_output.course = absb_it->second->target_catch.get_newest().cvl_course;
                fusion_result.fusion_target_output.uav_timestamp = absb_it->second->target_catch.get_newest().cvl_timestamp;
                fusion_result.fusion_target_output.confidence_level = 200.0f;
                // 将缓存的数据的置信度赋值为0
                absb_it->second->confidence_level = 0.0f;
                continue;
            }
            if (gradar_it != ground_radar_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = gradar_it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = gradar_it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = gradar_it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = gradar_it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = gradar_it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = gradar_it->second->target_catch.get_newest().uav_timestamp;
                fusion_result.fusion_target_output.confidence_level = 200.0f;
                // 将缓存的数据的置信度赋值为0
                gradar_it->second->confidence_level = 0.0f;
                continue;
            }
            LogError("target_ID_gr {} and target_ID_adsB {} is not found, in low_altitude_radar_target and adsB_target_map",
                     fusion_result.target_ID_gr, fusion_result.target_ID_adsB);
        }
        else if (fusion_result.target_ID_llr[0] != '\0' && fusion_result.target_ID_gr[0] != '\0' && fusion_result.target_ID_adsB[0] != '\0')
        {
            fusion_result.fusion_target_output.target_state = FusionType::RGRA;
            auto absb_it = adsB_target_map.find(std::string(fusion_result.target_ID_adsB));
            auto gradar_it = ground_radar_map.find(std::string(fusion_result.target_ID_gr));
            auto radar_it = low_altitude_map.find(std::string(fusion_result.target_ID_llr));
            if (absb_it != adsB_target_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = absb_it->second->target_catch.get_newest().cvl_longitude;
                fusion_result.fusion_target_output.uav_latitude = absb_it->second->target_catch.get_newest().cvl_latitude;
                fusion_result.fusion_target_output.uav_altitude = absb_it->second->target_catch.get_newest().cvl_altitude;
                fusion_result.fusion_target_output.speed = absb_it->second->target_catch.get_newest().cvl_speed;
                fusion_result.fusion_target_output.course = absb_it->second->target_catch.get_newest().cvl_course;
                fusion_result.fusion_target_output.uav_timestamp = absb_it->second->target_catch.get_newest().cvl_timestamp;
                fusion_result.fusion_target_output.confidence_level = 200.0f;
                // 将缓存的数据的置信度赋值为0
                absb_it->second->confidence_level = 0.0f;
                continue;
            }
            if (gradar_it != ground_radar_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = gradar_it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = gradar_it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = gradar_it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = gradar_it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = gradar_it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = gradar_it->second->target_catch.get_newest().uav_timestamp;
                fusion_result.fusion_target_output.confidence_level = 200.0f;
                // 将缓存的数据的置信度赋值为0
                gradar_it->second->confidence_level = 0.0f;
                continue;
            }
            if (radar_it != low_altitude_map.end())
            {
                fusion_result.fusion_target_output.uav_longitude = radar_it->second->target_catch.get_newest().radar_uav_longitude;
                fusion_result.fusion_target_output.uav_latitude = radar_it->second->target_catch.get_newest().radar_uav_latitude;
                fusion_result.fusion_target_output.uav_altitude = radar_it->second->target_catch.get_newest().radar_uav_altitude;
                fusion_result.fusion_target_output.speed = radar_it->second->target_catch.get_newest().radar_uav_speed;
                fusion_result.fusion_target_output.course = radar_it->second->target_catch.get_newest().radar_uav_course;
                fusion_result.fusion_target_output.uav_timestamp = radar_it->second->target_catch.get_newest().uav_timestamp;
                fusion_result.fusion_target_output.confidence_level = radar_it->second->confidence_level;
                // 将缓存的数据的置信度赋值为0
                radar_it->second->confidence_level = 0.0f;
                continue;
            }
            LogError("target_ID_llr {} and target_ID_gr {} target_ID_adsB{} is not found, in low_altitude_radar_target and adsB_target_map",
                     fusion_result.target_ID_llr, fusion_result.target_ID_gr, fusion_result.target_ID_adsB);
        }
        else
        { // BUG   
            LogError("target_ID_llr {} target_ID_gr {} target_ID_adsB {} is nullter!",
                     fusion_result.target_ID_llr, fusion_result.target_ID_gr, fusion_result.target_ID_adsB);
        }
    }
}

/**
 * @brief remove_repeat_Single_Sensor - 单传感器的目标去重
 * @param current_repeat_result - input, 传感器的ID对
 * @param sensor_type - input, 传感器的类型
 * @param repeat_result - output, 低空雷达数组ID对
 * @return 无返回值
 * @details
 * 0. 获取当前帧的低空雷达的ID对
 * 1. 对缓存历史ID对按照时间先后顺序进行排序
 * 2. 更新和删除缓存的历史ID对
 * 3. 将未匹配成功的目标插入到缓存的历史ID对
 */
void FusionMultiAlgKuerle::remove_repeat_Single_Sensor(std::vector<TargetRepeatAlg::RepeatResultMult> current_repeat_result,
                                                       std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_result,
                                                       const FusionType &sensor_type)
{
    std::vector<TargetRepeatAlg::RepeatResultMult>::iterator repeat_result_iter;
    for (repeat_result_iter = repeat_result.begin(); repeat_result_iter != repeat_result.end();)
    {
        bool is_update = false;
        std::vector<TargetRepeatAlg::RepeatResultMult>::iterator current_repeat_result_iter;
        for (current_repeat_result_iter = current_repeat_result.begin(); current_repeat_result_iter != current_repeat_result.end();)
        {
            if (sensor_type == FusionType::Radar)
            {
                // 检查目标ID是否为空或无效
                if (is_string_empty(current_repeat_result_iter->target_ID_llr))
                {
                    LogError("repeat_result_iter->target_ID_llr is {}", repeat_result_iter->target_ID_llr);
                }
                // 使用安全字符串比较函数，确保两个"0"字符串比较返回false
                if (safe_string_compare(repeat_result_iter->target_ID_llr, current_repeat_result_iter->target_ID_llr))
                {
                    memset(repeat_result_iter->target_ID_gr, 0, sizeof(repeat_result_iter->target_ID_gr));
                    memset(repeat_result_iter->target_ID_adsB, 0, sizeof(repeat_result_iter->target_ID_adsB));
                    current_repeat_result_iter = current_repeat_result.erase(current_repeat_result_iter);
                    is_update = true;
                    break;
                }
                else
                {
                    current_repeat_result_iter++;
                }
            }
            else if (sensor_type == FusionType::GroundRadar)
            {
                if (is_string_empty(current_repeat_result_iter->target_ID_gr))
                {
                    LogError("repeat_result_iter->target_ID_gr is {}", repeat_result_iter->target_ID_gr);
                }
                // 使用安全字符串比较函数，确保两个"0"字符串比较返回false
                if (safe_string_compare(repeat_result_iter->target_ID_gr, current_repeat_result_iter->target_ID_gr))
                {
                    memset(repeat_result_iter->target_ID_llr, 0, sizeof(repeat_result_iter->target_ID_llr));
                    memset(repeat_result_iter->target_ID_adsB, 0, sizeof(repeat_result_iter->target_ID_adsB));
                    current_repeat_result_iter = current_repeat_result.erase(current_repeat_result_iter);
                    is_update = true;
                    break;
                }
                else
                {
                    current_repeat_result_iter++;
                }
            }
            else if (sensor_type == FusionType::AdsB)
            {
                if (is_string_empty(current_repeat_result_iter->target_ID_adsB))
                {
                    LogError("repeat_result_iter->target_ID_adsB is {}", repeat_result_iter->target_ID_adsB);
                }
                // 使用安全字符串比较函数，确保两个"0"字符串比较返回false
                if (safe_string_compare(repeat_result_iter->target_ID_adsB, current_repeat_result_iter->target_ID_adsB))
                {
                    memset(repeat_result_iter->target_ID_llr, 0, sizeof(repeat_result_iter->target_ID_llr));
                    memset(repeat_result_iter->target_ID_gr, 0, sizeof(repeat_result_iter->target_ID_gr));
                    current_repeat_result_iter = current_repeat_result.erase(current_repeat_result_iter);
                    is_update = true;
                    break;
                }
                else
                {
                    current_repeat_result_iter++;
                }
            }
            else
            {
                LogError("remove repeat single sensor, sensor_type is {}, not GroundRadar or LowRadar or AdsB",
                         sensor_type);
                current_repeat_result_iter++;
            }
        }
        if (is_update == false)
        {
            repeat_result_iter = repeat_result.erase(repeat_result_iter);
        }
        else
        {
            repeat_result_iter++;
        }
    }
    repeat_result.insert(repeat_result.end(), current_repeat_result.begin(), current_repeat_result.end());
}



// 进行数据校验 检查fusion_ID是否在ID_before_fusion中，如果不在，则error
void FusionMultiAlgKuerle::check_fusion_ID(FusionTargetInfoArr *FusionTargetInfoOutput)
{
    for (size_t i = 0; i < FusionTargetInfoOutput->uavNum; i++)
    {
        bool found = false;
        // 遍历ID_before_fusion
        for (int j = 0; j < 4; j++)
        {
            std::string target_ID_str = std::string(FusionTargetInfoOutput->FusionTargetResultArr[i].ID_before_fusion[j]);
            std::string target_ID = target_ID_str.substr(0, target_ID_str.find("#"));
            // 使用安全字符串比较函数，确保两个"0"字符串比较返回false
            if (safe_string_compare(target_ID.c_str(), FusionTargetInfoOutput->FusionTargetResultArr[i].targetID))
            {
                found = true;
                break;
            }
        }
        if (!found)
        {
            LogWarn("fusion_ID {} not found in ID_before_fusion", FusionTargetInfoOutput->FusionTargetResultArr[i].targetID);
        }
    }
}

/**
 * @brief 检查std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_result中是否存在相同的aperance_ID
 * @details
 * 1. 遍历repeat_result
 * 2. 检查是否存在相同的aperance_ID
 * 3. 如果存在，则error
 */
void FusionMultiAlgKuerle::check_repeat_result(std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_result)
{
    for (size_t i = 0; i < repeat_result.size(); i++)
    {
        for (size_t j = i + 1; j < repeat_result.size(); j++)
        {
            if (safe_string_compare(repeat_result[i].first_apperance_ID, repeat_result[j].first_apperance_ID))
            {
                LogError("repeat_result has same first_apperance_ID {} and {}", repeat_result[i].first_apperance_ID, repeat_result[j].first_apperance_ID);
            }
        }
    }
}