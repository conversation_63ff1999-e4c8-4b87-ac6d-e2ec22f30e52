#include "fusion_alg_compute.h"
#include <fstream>
#include <iomanip>
#include <iostream>
#include "load_multisensor_data.h"
#include "load_multisensor_data_kuerle.h"
#include "fusion_multi_alg_kuerle.h"
#include "judge_valid_target.h"
#include "fusion_multi_alg.h"
#include "comm_tools.h"
#include "json.hpp"
#include "AlgLog.h"
#include "data_save2file.h"
#include "save_target_to_json_file.h"
#include <sstream>

int debug_count  = 0;
int threads_index_ = 0;										// 线程索引
extern int debug_count;
fusion_alg::fusion_alg() {

    AlgLog::Instance().InitAlgLog("Fusion_Algorithm" + std::to_string(threads_index_), "./FusionAlgData/Logs/alg_log.txt", 1);
    threads_index_++;

    // 初始化异步数据保存器
    async_data_saver_.start();

    //变量初始化
    param_from_json_.droneID_time_thres_ms = 6000;
    param_from_json_.spectrum_time_thres_ms = 6000;
    param_from_json_.radar_time_thres_ms = 6000;
    param_from_json_.droneID_confidence_factor = 100;
    param_from_json_.radar_confidence_factor = 10;
    param_from_json_.radar_confidence_factor_time = 1000;
    param_from_json_.spectrum_confidence_factor = 30;
    param_from_json_.dis_radar_droneID_thres = 150;
    param_from_json_.droneID_keep_times_ms = 20000;
    param_from_json_.radar_keep_time_ms = 20000;
    param_from_json_.spectrum_keep_time_ms = 15000;
    param_from_json_.max_single_droneID_confidence_factor = 100;
    param_from_json_.max_single_radar_confidence_factor = 100;
    param_from_json_.max_single_spectrum_confidence_factor = 100;
    param_from_json_.RTarget_specDevice_dis_thres = 8000;
    param_from_json_.test_mod_open = 1;
    param_from_json_.log_level = 1;
    param_from_json_.AOA_fusion_flag = 1;
    param_from_json_.aoa_freq_thres_Mhz = 40;
    param_from_json_.aoa_single_azimuth_thres = 20;
    param_from_json_.aoa_time_thres_ms = 6000; 
    param_from_json_.aoa_num_min_aoa_device = 3;
    param_from_json_.aoa_min_azi_rmse = 5;
    param_from_json_.aoa_track_dis_thres_m = 400;
    param_from_json_.aoa_track_dis_per_1km_m = 50;
    param_from_json_.aoa_track_dis_center_target_thres_m = 8000;
    param_from_json_.aoa_tarck_dis_limit_aoa_m = 110;

    param_from_json_.fragmented_distance_thres = 100;
    param_from_json_.fragmented_speed_dis_thres = 0.15;


	//初始化ID，避免在Linux系统下出现内存错误
	char a[] = "NULL";
	for (int i = 0; i < 20; i++) {
        STRCPY(multi_spectrum_target_info_arr_[i].spectrum_device_ID, a);
        STRCPY(droneID_deocde_info_arr_[i].target_ID, a);
        STRCPY(radar_target_info_arr_[i].target_ID, a);
        STRCPY(fusion_target_save_arr_[i].target_ID, a);
		for (int j = 0; j < 20; j++) {
			STRCPY(multi_spectrum_target_info_arr_[i].spectrum_target_info_arr_[j].target_ID, a);
		}
	}
    memset(&multi_sensor_info_save_arr_, 0, sizeof(multi_sensor_info_save_arr_));

    //插值计时器
    //interp_timer_(1000);
    interp_timer_.set_interval_ms(500);
    interp_timer_.set_fusion_target_info_arr(fusion_target_save_arr_);
    interp_timer_.start();
}

fusion_alg::~fusion_alg() {
    // 停止异步数据保存器，等待队列中的数据处理完成
    async_data_saver_.stop(true);

    interp_timer_.stop();

    // 输出异步保存器的最终统计信息
    auto stats = async_data_saver_.get_queue_stats();
    LogInfo("AsyncDataSaver final statistics - Enqueued: {}, Processed: {}, Dropped: {}, Errors: {}",
            stats.total_enqueued, stats.total_processed,
            stats.total_dropped, stats.total_errors);
}

int fusion_alg::Multisensor_Device_init(MultiSensorInfoArr* MultiSensorInfo) {

    multi_sensor_info_arr_ = *MultiSensorInfo;
    LogDebug("Multisensor_Device_init!");
    for (int i = 0; i < MultiSensorInfo->RadarDeviceNum; i++) {
        LogDebug("i {}, RadarDeviceID = {}",i, MultiSensorInfo->RadarDeviceInfoArr[i].RadarDeviceID);
        LogDebug("i {}, RadarDeviceAltitude = {}", i, MultiSensorInfo->RadarDeviceInfoArr[i].RadarDeviceAltitude);
        LogTrace("i {}, RadarDeviceAzimuthAccuracy = {}", i, MultiSensorInfo->RadarDeviceInfoArr[i].RadarDeviceAzimuthAccuracy);
        LogTrace("i {}, RadarDeviceDistanceAccuracy = {}", i, MultiSensorInfo->RadarDeviceInfoArr[i].RadarDeviceDistanceAccuracy);
        LogTrace("i {}, RadarDeviceElevationAccuracy = {}", i, MultiSensorInfo->RadarDeviceInfoArr[i].RadarDeviceElevationAccuracy);
        LogDebug("i {}, RadarDeviceLatitude = {}", i, MultiSensorInfo->RadarDeviceInfoArr[i].RadarDeviceLatitude);
        LogDebug("i {}, RadarDeviceLongitude = {}", i, MultiSensorInfo->RadarDeviceInfoArr[i].RadarDeviceLongitude);
    }

    for (int i = 0; i < MultiSensorInfo->SpectrumDeviceNum; i++) {
        LogDebug("i {}, SpectrumDeviceID = {}", i, MultiSensorInfo->SpectrumDeviceInfoArr[i].SpectrumDeviceID);
        LogDebug("i {}, SpectrumDeviceAltitude = {}", i, MultiSensorInfo->SpectrumDeviceInfoArr[i].SpectrumDeviceAltitude);
        LogTrace("i {}, SpectrumDeviceAzimuthAccuracy = {}", i, MultiSensorInfo->SpectrumDeviceInfoArr[i].SpectrumDeviceAzimuthAccuracy);
        LogDebug("i {}, SpectrumDeviceLatitude = {}", i, MultiSensorInfo->SpectrumDeviceInfoArr[i].SpectrumDeviceLatitude);
        LogDebug("i {}, SpectrumDeviceLongitude = {}", i, MultiSensorInfo->SpectrumDeviceInfoArr[i].SpectrumDeviceLongitude);
    }

    //LogInfo("Multi sensor device info init successed!");

    return 1;
}

int fusion_alg::Algorithm_Configuration_Overload(const char* config_path,int8_t* overload_flag) {
    //从文件中读取json数组
    //printf("overload_flag=%d,config_path=%s\n", *overload_flag,config_path);
    LogInfo("overload_flag ={},config_path={}" ,*overload_flag, config_path);
    if (*overload_flag == 1) {
        std::ifstream readFile(config_path);
        if (!readFile.is_open()) {
            printf("Error: json file open failed!");
            return -1;
        }

        nlohmann::json json_data;
        //auto json_show = json_data.dump(1);
        //LogInfo("{}", json_show);
        auto file_state = readFile.good();
        if (file_state == true) {
            json_data = nlohmann::json::parse(readFile);
            readFile.close();
        }
        nlohmann::json alg_param_json = json_data.at("alg_param");
 
        alg_param_json.at("test_mod_open").get_to(param_from_json_.test_mod_open);
        alg_param_json.at("log_level").get_to(param_from_json_.log_level);
        alg_param_json.at("droneID_time_thres_ms").get_to(param_from_json_.droneID_time_thres_ms);
        alg_param_json.at("spectrum_time_thres_ms").get_to(param_from_json_.spectrum_time_thres_ms);
        alg_param_json.at("radar_time_thres_ms").get_to(param_from_json_.radar_time_thres_ms);
        alg_param_json.at("droneID_confidence_factor").get_to(param_from_json_.droneID_confidence_factor);
        alg_param_json.at("spectrum_confidence_factor").get_to(param_from_json_.spectrum_confidence_factor);
        alg_param_json.at("radar_confidence_factor").get_to(param_from_json_.radar_confidence_factor);
        alg_param_json.at("radar_confidence_factor_time").get_to(param_from_json_.radar_confidence_factor_time);
        alg_param_json.at("dis_radar_droneID_thres").get_to(param_from_json_.dis_radar_droneID_thres);
        alg_param_json.at("spectrum_keep_time_ms").get_to(param_from_json_.spectrum_keep_time_ms);
        alg_param_json.at("radar_keep_time_ms").get_to(param_from_json_.radar_keep_time_ms);
        alg_param_json.at("droneID_keep_times_ms").get_to(param_from_json_.droneID_keep_times_ms);
        alg_param_json.at("max_single_droneID_confidence_factor").get_to(param_from_json_.max_single_droneID_confidence_factor);
        alg_param_json.at("max_single_spectrum_confidence_factor").get_to(param_from_json_.max_single_spectrum_confidence_factor);
        alg_param_json.at("max_single_radar_confidence_factor").get_to(param_from_json_.max_single_radar_confidence_factor);
        alg_param_json.at("RTarget_specDevice_dis_thres").get_to(param_from_json_.RTarget_specDevice_dis_thres);
        alg_param_json.at("airport_type").get_to(param_from_json_.airport_type);
  
        nlohmann::json aoa_param_json = json_data.at("aoa_param");
  
        aoa_param_json.at("AOA_fusion_flag").get_to(param_from_json_.AOA_fusion_flag);
        aoa_param_json.at("aoa_time_thres_ms").get_to(param_from_json_.aoa_time_thres_ms);
        aoa_param_json.at("aoa_freq_thres_Mhz").get_to(param_from_json_.aoa_freq_thres_Mhz);
        aoa_param_json.at("aoa_single_azimuth_thres").get_to(param_from_json_.aoa_single_azimuth_thres);
        aoa_param_json.at("aoa_track_dis_thres_m").get_to(param_from_json_.aoa_track_dis_thres_m);
        aoa_param_json.at("aoa_num_min_aoa_device").get_to(param_from_json_.aoa_num_min_aoa_device);
        aoa_param_json.at("aoa_min_azi_rmse").get_to(param_from_json_.aoa_min_azi_rmse);
        aoa_param_json.at("aoa_track_dis_per_1km_m").get_to(param_from_json_.aoa_track_dis_per_1km_m);
        aoa_param_json.at("aoa_track_dis_center_target_thres_m").get_to(param_from_json_.aoa_track_dis_center_target_thres_m);
        aoa_param_json.at("aoa_tarck_dis_limit_aoa_m").get_to(param_from_json_.aoa_tarck_dis_limit_aoa_m);

        nlohmann::json rgra_param_json = json_data.at("rgra_param");
        rgra_param_json.at("ground_radrar_keep_time_thres").get_to(param_from_json_.ground_radrar_keep_time_thres);
        rgra_param_json.at("low_altitude_radar_keep_time_thres").get_to(param_from_json_.low_altitude_radar_keep_time_thres);
        rgra_param_json.at("adsB_keep_time_thres").get_to(param_from_json_.adsB_keep_time_thres);
        rgra_param_json.at("fragmented_distance_thres").get_to(param_from_json_.fragmented_distance_thres);
        rgra_param_json.at("fragmented_speed_dis_thres").get_to(param_from_json_.fragmented_speed_dis_thres);
        auto json_show = json_data.dump(1);
        LogInfo("fusion_param:{}", json_show);
    }



    return 1;
}

int fusion_alg::save_fusion_info(FusionTargetInfoArr* fusion_target_info_arr,const int max_num_target,int curr_sensor_type) {
    int ret = 0;
    LogDebug("save fusion info, curr_sensor_type {}", curr_sensor_type);

   // LogDebug("TEST: fusion_target_save_arr_[0].target_ID = {}", fusion_target_save_arr_[0].target_ID);

    for (int i = 0; i < fusion_target_info_arr->uavNum; i++) {
        // 如果不是单个站点的目标
        int lats_time_index = 0;
        if ((fusion_target_info_arr->FusionTargetResultArr[i].target_state != Spectrum)
            && (fusion_target_info_arr->FusionTargetResultArr[i].target_state != Radar)
           /* && (fusion_target_info_arr->FusionTargetResultArr[i].target_state != DroneID)*/) {
            //将融合目标存入数组
            int match_flag = 0;
            int64_t timestamp[100] = { 0 };


            for (int j = 0; j < max_num_target; j++) {
                FusionTargetInfo newest_data = fusion_target_save_arr_[j].fusion_target_save_info.get_newest();
                timestamp[j] = newest_data.uav_timestamp;
                if (timestamp[j] > 0) 
                {
                    int strcmp_flag = strcmp(fusion_target_info_arr->FusionTargetResultArr[i].targetID,
                        fusion_target_save_arr_[j].target_ID);
                    // 融合前ID也要匹配
                    int strcmp_flag2 = 1;
                    // 确保协议破解和雷达的存储目标不重复
                    for (int num_id = 0; num_id < 2; num_id++) {
                        strcmp_flag2 = strcmp(fusion_target_info_arr->FusionTargetResultArr[i].ID_before_fusion[num_id],
                            newest_data.ID_before_fusion[num_id]);
                        if (strcmp_flag2 == 0 && fusion_target_info_arr->FusionTargetResultArr[i].ID_before_fusion[num_id][0] != '\0') {
                            break;
                        }
                        else {
                            strcmp_flag2 = 1;
                        }
                    }
                    // ID匹配
                    if (strcmp_flag == 0 || 
                        (strcmp_flag2 == 0&& fusion_target_info_arr->FusionTargetResultArr[i].uav_serial_number[0]=='\0')) 
                    {
                        LogDebug("strcmp_flag={},j={},i={}, target_id = {},j={},target_id{}", strcmp_flag, j,
                            i, fusion_target_info_arr->FusionTargetResultArr[i].targetID, j, fusion_target_save_arr_[j].target_ID);
                        if (fusion_target_info_arr->FusionTargetResultArr[i].confidence_level < fusion_target_save_arr_[j].fusion_target_save_info.get_newest().confidence_level)
                        {
                            #ifdef AlgTest
                            if(fabs(fusion_target_info_arr->FusionTargetResultArr[i].confidence_level - 99.0f) > 1e-4){
                                fusion_target_info_arr->FusionTargetResultArr[i].confidence_level =
                                    fusion_target_save_arr_[j].fusion_target_save_info.get_newest().confidence_level;
                            }
                            #else
                            fusion_target_info_arr->FusionTargetResultArr[i].confidence_level =
                                fusion_target_save_arr_[j].fusion_target_save_info.get_newest().confidence_level;
                            #endif
                        }

                        int arr_len = fusion_target_save_arr_[i].fusion_target_save_info.get_length();
                        int curr_index = fusion_target_save_arr_[i].fusion_target_save_info.get_newest_index();
                        int pre_index = (curr_index + arr_len - 1) % arr_len;
                        int64_t pre_timestamp = fusion_target_save_arr_[i].fusion_target_save_info[curr_index].uav_timestamp;
                        int64_t time_diff = abs(fusion_target_info_arr->FusionTargetResultArr[i].uav_timestamp - pre_timestamp);
                        if (time_diff == 0) {
                            match_flag = 1;
                            break;
                        }
                        fusion_target_save_arr_[j].interp_count = 0;
                        fusion_target_save_arr_[j].fusion_target_save_info.push(fusion_target_info_arr->FusionTargetResultArr[i]);
                        LogDebug("idx {}, Newsest {}, latlon {},{} ", j,
                            fusion_target_save_arr_[j].fusion_target_save_info.get_newest_index(),
                            fusion_target_save_arr_[j].fusion_target_save_info.get_newest().uav_latitude,
                            fusion_target_save_arr_[j].fusion_target_save_info.get_newest().uav_longitude);
                        STRCPY(fusion_target_info_arr->FusionTargetResultArr[i].targetID, fusion_target_save_arr_[j].target_ID);
                        // 20240824 不改变ID，只使用第一次匹配上的ID
                        fusion_target_save_arr_[j].is_used = 0;
                        match_flag = 1;
                        lats_time_index = j;
                    }
                }
            }
      



            if (match_flag != 1) {
                //找时间戳最小的值，存入该目标数组中
                int min_index = 0;
                int64_t min_timestamp = findMinValue(timestamp, max_num_target, min_index);
                if (fusion_target_info_arr->FusionTargetResultArr[i].confidence_level < fusion_target_save_arr_[min_index].fusion_target_save_info.get_newest().confidence_level)
                {
                    fusion_target_info_arr->FusionTargetResultArr[i].confidence_level =
                        fusion_target_save_arr_[min_index].fusion_target_save_info.get_newest().confidence_level;
                }
                fusion_target_save_arr_[min_index].interp_count = 0;
                fusion_target_save_arr_[min_index].fusion_target_save_info.push(fusion_target_info_arr->FusionTargetResultArr[i]);

                LogDebug("idx {}, Newsest {}, latlon{},{} ", min_index,
                    fusion_target_save_arr_[min_index].fusion_target_save_info.get_newest_index(),
                    fusion_target_save_arr_[min_index].fusion_target_save_info.get_newest().uav_latitude,
                    fusion_target_save_arr_[min_index].fusion_target_save_info.get_newest().uav_longitude);

                STRCPY(fusion_target_save_arr_[min_index].target_ID, fusion_target_info_arr->FusionTargetResultArr[i].targetID);
                fusion_target_save_arr_[min_index].is_used = 0;
                LogDebug("min_index = {}, fusion_target_save_arr_[min_index].timestamp = {}", min_index, 
                    fusion_target_save_arr_[min_index].fusion_target_save_info.get_newest().uav_timestamp);
                lats_time_index = min_index;
            }
            // 更新最后保存的时间戳
            if (curr_sensor_type == DroneID) {
                fusion_target_save_arr_[lats_time_index].droneID_save_last_time = 
                    fusion_target_info_arr->FusionTargetResultArr[i].uav_timestamp;
                LogDebug("lats_time_index = {}, droneID_save_last_time = {}", 
                    lats_time_index, fusion_target_save_arr_[lats_time_index].droneID_save_last_time);
            }
            if (curr_sensor_type == Radar) {
                fusion_target_save_arr_[lats_time_index].radar_save_last_time = 
                    fusion_target_info_arr->FusionTargetResultArr[i].uav_timestamp;
                LogDebug("lats_time_index = {}, radar_save_last_time = {}", 
                    lats_time_index, fusion_target_save_arr_[lats_time_index].radar_save_last_time);
            }
            if (curr_sensor_type == Spectrum) {
                fusion_target_save_arr_[lats_time_index].spectrum_save_last_time = 
                    fusion_target_info_arr->FusionTargetResultArr[i].uav_timestamp;
                LogDebug("lats_time_index = {}, spectrum_save_last_time = {}", 
                    lats_time_index, fusion_target_save_arr_[lats_time_index].spectrum_save_last_time);
            }
        }
    }
    return ret;
}

int fusion_alg::state_rejudge(const int max_target,const int64_t curr_time) {
    //超过时间阈值，认为状态已经无效，不需要保持了
    //前期只需要判断雷达的状态
    int ret = 0;
    // 通过频谱ID找最后的出现时间

    for (int i = 0; i < max_target; i++) {
        if (fusion_target_save_arr_[i].target_ID[0] != '\0' && fusion_target_save_arr_[i].radar_save_last_time != 0) {
            float time_diff = abs(fusion_target_save_arr_[i].radar_save_last_time - curr_time);

			if (time_diff > param_from_json_.radar_keep_time_ms) {
				//超过了时间阈值，去掉雷达的状态
				FusionTargetInfo info_tmp = fusion_target_save_arr_[i].fusion_target_save_info.get_newest();
				LogInfo("Radar info timeout, clear radar info,i={}, last_time = {}, curr_time = {}", i,fusion_target_save_arr_[i].radar_save_last_time, curr_time);
				if (info_tmp.target_state == RadarAndDroneID || info_tmp.target_state == SpectrumAndRadar) {
					LogInfo("Curr state={}, clear this arr", info_tmp.target_state);
					//清空这个状态
					char a[] = "NULL";
					fusion_target_save_arr_[i].is_fusioned = 0;
					fusion_target_save_arr_[i].is_used = 1;
					fusion_target_save_arr_[i].radar_save_last_time = 0;
					STRCPY(fusion_target_save_arr_[i].target_ID, a);
					fusion_target_save_arr_[i].fusion_target_save_info.reset();
					ret = 1;
				}
				else if (info_tmp.target_state == DSR) {
					// 切换为SpectrumAndDroneID
					LogInfo("Curr state is DSR, trans to SpectrumAndDroneID");
					info_tmp.target_state = SpectrumAndDroneID;
					memset(info_tmp.ID_before_fusion[1], 0, sizeof(info_tmp.ID_before_fusion[1]));
					fusion_target_save_arr_[i].fusion_target_save_info.push(info_tmp);
					ret = 1;
				}
              
			}
		}

        if (fusion_target_save_arr_[i].target_ID[0] != '\0'&& fusion_target_save_arr_[i].spectrum_save_last_time!=0) {
            float time_diff = abs(fusion_target_save_arr_[i].spectrum_save_last_time - curr_time);
			if (time_diff > param_from_json_.spectrum_keep_time_ms) {
				//超过了时间阈值，去掉频谱的状态
				FusionTargetInfo info_tmp = fusion_target_save_arr_[i].fusion_target_save_info.get_newest();
				//LogInfo("Spectrum info timeout, clear radar info");
				LogInfo("Spectrum info timeout, clear fusion info,i={}, last_time = {}, curr_time = {}", i , fusion_target_save_arr_[i].spectrum_save_last_time, curr_time);
				if (info_tmp.target_state == SpectrumAndRadar) {
					LogInfo("Curr state={}, clear this arr", info_tmp.target_state);
					//清空这个状态
					char a[]="NULL";
					fusion_target_save_arr_[i].is_fusioned = 0;
					fusion_target_save_arr_[i].is_used = 1;
					fusion_target_save_arr_[i].spectrum_save_last_time = 0;
					STRCPY(fusion_target_save_arr_[i].target_ID, a);
					fusion_target_save_arr_[i].fusion_target_save_info.reset();
					ret = 1;
				} else if (info_tmp.target_state == AOA) {
                    LogInfo("Curr state={}, clear this arr", info_tmp.target_state);
                    //清空这个状态
                    char a[] = "NULL";
                    fusion_target_save_arr_[i].is_fusioned = 0;
                    fusion_target_save_arr_[i].is_used = 1;
                    fusion_target_save_arr_[i].spectrum_save_last_time = 0;
                    STRCPY(fusion_target_save_arr_[i].target_ID, a);
                    fusion_target_save_arr_[i].fusion_target_save_info.reset();
                    ret = 1;
                }
			}
		}

        //超过协议破解时间
        if (fusion_target_save_arr_[i].target_ID[0] != '\0' && fusion_target_save_arr_[i].droneID_save_last_time != 0) {
            float time_diff = abs(fusion_target_save_arr_[i].droneID_save_last_time - curr_time);

			if (time_diff > param_from_json_.droneID_keep_times_ms) {
				//超过了时间阈值，去掉协议破解的状态
				FusionTargetInfo info_tmp = fusion_target_save_arr_[i].fusion_target_save_info.get_newest();
				//LogInfo("DroneID info timeout, clear radar info");
				LogInfo("DroneID info timeout, clear radar info,i={}, last_time = {}, curr_time = {}", i, fusion_target_save_arr_[i].droneID_save_last_time, curr_time);

				if (info_tmp.target_state == SpectrumAndDroneID ) {
					LogInfo("Curr state={}, clear this arr", info_tmp.target_state);
					//清空这个状态
					char a[] = "NULL";
					fusion_target_save_arr_[i].is_fusioned = 0;
					fusion_target_save_arr_[i].is_used = 1;
					fusion_target_save_arr_[i].radar_save_last_time = 0;
					fusion_target_save_arr_[i].droneID_save_last_time = 0;
					STRCPY(fusion_target_save_arr_[i].target_ID, a);
					fusion_target_save_arr_[i].fusion_target_save_info.reset();
					ret = 1;
				}
			}
		}
    }
    return ret;
}



void printf_fusion_target_info(FusionTargetInfoArr* FusionTargetInfoOutput) {

    //TODO 调试信息
    debug_count ++;
    if(debug_count > 1000000)
    {
        debug_count = 0;
    }
    //TODO 调试信息

    for (int i = 0; i < FusionTargetInfoOutput->uavNum; i++) {
        LogDebug("debug_count={} targetID={} azimuth={} speed={} uav_altitude={} uav_latitude={} uav_longitude={} uav_serial_number={} uav_timestamp={} ID_before_fusion1={} ID_before_fusion2={} ID_before_fusion3={} ID_before_fusion4={} ID_before_fusion5={} fusion_ID={} fusion type={} confidence_level={}",
                 debug_count,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].targetID,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].azimuth,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].speed,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].uav_altitude,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].uav_latitude,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].uav_longitude,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].uav_serial_number,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].uav_timestamp,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].ID_before_fusion[0],
                 FusionTargetInfoOutput->FusionTargetResultArr[i].ID_before_fusion[1],
                 FusionTargetInfoOutput->FusionTargetResultArr[i].ID_before_fusion[2],
                 FusionTargetInfoOutput->FusionTargetResultArr[i].ID_before_fusion[3],
                 FusionTargetInfoOutput->FusionTargetResultArr[i].ID_before_fusion[4],
                 FusionTargetInfoOutput->FusionTargetResultArr[i].targetID,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].target_state,
                 FusionTargetInfoOutput->FusionTargetResultArr[i].confidence_level);
    }
}


void printf_input_target_info(MultiSensorTargetInputInfoArr* MultiSensorTargetInput) {
    // 打印雷达目标信息
    if (MultiSensorTargetInput->RadarTargetNum > 0) {
        for (int i = 0; i < MultiSensorTargetInput->RadarTargetNum; i++) {
            LogDebug("雷达目标[{}]: ID={} 经度={} 纬度={} 方位角={} 航向={} 高度={} 速度={} 时间戳={} 设备ID={}",
                i,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_uav_ID,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_uav_longitude,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_uav_latitude,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_uav_azimuth,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_uav_course,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_uav_altitude,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_uav_speed,
                MultiSensorTargetInput->RadarTargetInfoArr[i].uav_timestamp,
                MultiSensorTargetInput->RadarTargetInfoArr[i].radar_device_ID);
        }
    }

    // 打印频谱目标信息
    if (MultiSensorTargetInput->SpectrumTargetNum > 0) {
        for (int i = 0; i < MultiSensorTargetInput->SpectrumTargetNum; i++) {
            LogDebug("频谱目标[{}]: 方位角={} 距离={} 带宽={} 中心频率={} 时间戳={} 组ID={}",
                i,
                MultiSensorTargetInput->SpectrumTargetInfoArr[i].spectrum_uav_azimuth,
                MultiSensorTargetInput->SpectrumTargetInfoArr[i].spectrum_uav_distance,
                MultiSensorTargetInput->SpectrumTargetInfoArr[i].spectrum_uav_bandwidth,
                MultiSensorTargetInput->SpectrumTargetInfoArr[i].spectrum_uav_center_freq,
                MultiSensorTargetInput->SpectrumTargetInfoArr[i].uav_timestamp,
                MultiSensorTargetInput->SpectrumTargetInfoArr[i].spectrum_group_ID);
        }
    }

    // 打印无人机ID解码信息
    if (MultiSensorTargetInput->SpectrumPositioningTargetNum > 0) {
        for (int i = 0; i < MultiSensorTargetInput->SpectrumPositioningTargetNum; i++) {
            LogDebug("无人机ID[{}]: 序列号={} 类型={} 经度={} 纬度={} 高度={} 时间戳={}",
                i,
                MultiSensorTargetInput->SpectrumPositioningTargetInfoArr[i].uav_serial_number,
                MultiSensorTargetInput->SpectrumPositioningTargetInfoArr[i].uav_type,
                MultiSensorTargetInput->SpectrumPositioningTargetInfoArr[i].uav_longitude,
                MultiSensorTargetInput->SpectrumPositioningTargetInfoArr[i].uav_latitude,
                MultiSensorTargetInput->SpectrumPositioningTargetInfoArr[i].uav_altitude,
                MultiSensorTargetInput->SpectrumPositioningTargetInfoArr[i].uav_timestamp);
        }
    }

    // 打印地面雷达目标信息
    if (MultiSensorTargetInput->GRadarTargetNum > 0) {
        for (int i = 0; i < MultiSensorTargetInput->GRadarTargetNum; i++) {
            LogDebug("地面雷达目标[{}]: ID={} 经度={} 纬度={} 方位角={} 航向={} 高度={} 速度={} 时间戳={} 设备ID={}",
                i,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_uav_ID,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_uav_longitude,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_uav_latitude,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_uav_azimuth,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_uav_course,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_uav_altitude,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_uav_speed,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].uav_timestamp,
                MultiSensorTargetInput->GRadarTargetInfoArr[i].radar_device_ID);
        }
    }

    // 打印ADS-B目标信息
    if (MultiSensorTargetInput->AdsBPositioningTargetNum > 0) {
        for (int i = 0; i < MultiSensorTargetInput->AdsBPositioningTargetNum; i++) {
            LogDebug("ADS-B目标[{}]: ID={} 经度={} 纬度={} 高度={} 航向={} 速度={} 上升率={} 时间戳={} 呼号={} 设备ID={}",
                i,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].targetID,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_longitude,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_latitude,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_altitude,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_course,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_speed,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_uprate,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_timestamp,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].cvl_callsign,
                MultiSensorTargetInput->AdsBPositioningTargetInfoArr[i].adsB_device_ID);
        }
    }
}


void printf_repeat_result(const std::vector<TargetRepeatAlg::RepeatResultMult> &repeat_result,
                          const std::vector<TargetRepeatAlg::RepeatResultDouble> &repeat_gr2gr_result) {
    // 使用stringstream构建字符串
    std::stringstream ss;
    ss << "总数量:" << repeat_result.size();
    ss << " 去重结果: \n";
    for (const auto &repeat_result_ : repeat_result) {
        // std::string print_id = "F895C24AS0074QYK";
        ss << "("
           << "[" << repeat_result_.target_ID_llr << "]" << ", "
           << "[" << repeat_result_.target_ID_gr << "]" << ", "
           << "[" << repeat_result_.target_ID_adsB << "]" << ", "
           << "[" << repeat_result_.start_time << "]" << ", "
           << "[" << repeat_result_.first_apperance_ID << "]" << "), \n";
    }
    ss << "总数量:" << repeat_gr2gr_result.size();
    ss << " 地面雷达去重结果: \n";
    for (const auto &repeat_result_ : repeat_gr2gr_result) {
        // std::string print_id = "F895C24AS0074QYK";
        ss << "("
           << "[" << repeat_result_.target_ID1 << "]" << ", "
           << "[" << repeat_result_.target_ID2 << "]" << ", "
           << "[" << repeat_result_.start_time << "]" << ", "
           << "[" << repeat_result_.first_apperance_ID << "]" << "), \n";
    }
    // 获取完整字符串并打印
    LogDebug("{}", ss.str());
}
   

int fusion_alg::search_spec_device_index(MultiSpectrumTargetInfoArr* multi_spectrum_target_info_arr,int max_num_device,int max_num_target) {

    int64_t timestamp1[100] = { 0 };
    int64_t timestamp2[100] = { 0 };
    for (int i = 0; i < max_num_device; i++) {
        for (int j = 0; j < max_num_target; j++) {
            SpectrumTargetInfo single_target = multi_spectrum_target_info_arr[i].spectrum_target_info_arr_[j].spectrum_target_info.get_newest();
            timestamp1[j] = single_target.uav_timestamp;
        }
        int max_index = 0;
        timestamp2[i] = findMaxValue(timestamp1, max_num_target, max_index);
        LogTrace("Max timestamp every device ,device_index = {}, max_index = {}, timestamp = {}",i,max_index, timestamp2[i]);
    }


    int device_index = 0;

    int64_t max_value = findMaxValue(timestamp2, max_num_device, device_index);
    LogDebug("Search spec device index = {},timestamp = {}", device_index, max_value);
    return device_index;
}

void save_spec_station_info(MultiSensorInfoArr* multi_device_info_input, 
                            MultiSensorInfoArr* multi_device_info_save, 
                            int max_station_num)
{
    int is_find = 0;
    for (int i = 0; i < multi_device_info_input->SpectrumDeviceNum; i++) 
    {
        for (int j = 0; j < max_station_num; j++)
        {
            int strcmp_flag = strcmp(multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceID,
                multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceID);
            // LogDebug(" i {}, max_station_num {}, strcmp_flag {}", i, max_station_num, strcmp_flag);
            if (strcmp_flag == 0 && multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceID[0] != '\0')
            {
                // 找到了已存储过的数组
                multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceLatitude = 
                    multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceLatitude;
                multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceLongitude =
                    multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceLongitude;
                LogTrace("Same sta {}, ID {}, latlon {},{}", i, multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceID,
                    multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceLatitude, 
                    multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceLongitude);
                is_find = 1;
                break;
            }
        }

        if (is_find == 0)
        {
            for (int j = 0; j < max_station_num; j++)
            {
                // LogDebug(" i {}, max_station_num {}, timestamp {}", i, max_station_num, station_save_info[i].timestamp);
                if (multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceLatitude == 0&&
                    multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceID[0] != '\0')
                {
                    STRCPY(multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceID, 
                        multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceID);

                    multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceLatitude = 
                        multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceLatitude;
                    multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceLongitude =
                        multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceLongitude;
                    multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceAltitude =
                        multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceAltitude;
                    multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceAzimuthAccuracy =
                        multi_device_info_input->SpectrumDeviceInfoArr[i].SpectrumDeviceAzimuthAccuracy;
                    multi_device_info_save->SpectrumDeviceNum++;
                    LogDebug("New sta {}, ID {}, latlon {},{}", j, multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceID,
                        multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceLatitude, 
                        multi_device_info_save->SpectrumDeviceInfoArr[j].SpectrumDeviceLongitude);
                    break;
                }
            }
        }
    }

    for (int i = 0; i < multi_device_info_input->RadarDeviceNum; i++)
    {
        for (int j = 0; j < max_station_num; j++)
        {
            int strcmp_flag = strcmp(multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceID,
                multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceID);
            // LogDebug(" i {}, max_station_num {}, strcmp_flag {}", i, max_station_num, strcmp_flag);
            if (strcmp_flag == 0 && multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceID[0] != '\0')
            {
                // 找到了已存储过的数组
                multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceLatitude =
                    multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceLatitude;
                multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceLongitude =
                    multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceLongitude;
                LogTrace("Same sta {}, ID {}, latlon {},{}", i, multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceID,
                    multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceLatitude,
                    multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceLongitude);
                is_find = 1;
                break;
            }
        }

        if (is_find == 0)
        {
            for (int j = 0; j < max_station_num; j++)
            {
                // LogDebug(" i {}, max_station_num {}, timestamp {}", i, max_station_num, station_save_info[i].timestamp);
                if (multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceLatitude == 0
                    && multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceID[0] != '\0')
                {
                    STRCPY(multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceID,
                        multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceID);

                    multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceLatitude =
                        multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceLatitude;
                    multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceLongitude =
                        multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceLongitude;
                    multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceAltitude =
                        multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceAltitude;
                    multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceAzimuthAccuracy =
                        multi_device_info_input->RadarDeviceInfoArr[i].RadarDeviceAzimuthAccuracy;
                    multi_device_info_save->RadarDeviceNum++;
                    LogDebug("New sta {}, ID {}, latlon {},{}", i, multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceID,
                        multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceLatitude,
                        multi_device_info_save->RadarDeviceInfoArr[j].RadarDeviceLongitude);
                    break;
                }
            }
        }
    }
    
    
}


void get_center_latlon(MultiSensorInfoArr* multi_device_info_save, float* center_latlon) {

    if (multi_device_info_save->RadarDeviceNum > 0) {

        center_latlon[0] = multi_device_info_save->RadarDeviceInfoArr[0].RadarDeviceLatitude;
        center_latlon[1] = multi_device_info_save->RadarDeviceInfoArr[0].RadarDeviceLongitude;
        LogDebug("Use spec, center latlon {},{}", center_latlon[0], center_latlon[1]);
    }
    else {

        center_latlon[0] = multi_device_info_save->SpectrumDeviceInfoArr[0].SpectrumDeviceLatitude;
        center_latlon[1] = multi_device_info_save->SpectrumDeviceInfoArr[0].SpectrumDeviceLongitude;
        LogDebug("Use radar, center latlon {},{}", center_latlon[0], center_latlon[1]);
    }


}

void print_kuerle(const std::vector<LowAltitudeRadarTarget> &low_altitude_radar_target_arr,
                  const std::vector<GroundRadarTarget> &ground_radar_target_arr,
                  const std::vector<AdsBPositioningTarget> &adsB_target_arr)
{
    // 打印低空雷达目标信息
    LogInfo("低空雷达目标数量: {} 地面雷达目标数量: {} ADS-B目标数量: {}",
            low_altitude_radar_target_arr.size(),
            ground_radar_target_arr.size(),
            adsB_target_arr.size());
}

int fusion_alg::fusion_Alg_Compute_CHONGQING(void *uav_alg_handle, MultiSensorTargetInputInfoArr* MultiSensorTargetInput, 
	FusionTargetInfoArr* FusionTargetInfoOutput,OutputTarget_Callback callBackTmp, int32_t* fusion_status, FusionParam& fusion_param){

    interp_timer_.set_cb(callBackTmp);
    interp_timer_.set_alg_handle(uav_alg_handle);


	int max_target_save = 100;  //最大的保存目标数量，不超过20
    int max_spectrum_save = 10;  //最大的保存频谱目标数量，不超过10
	int arr_reset_time_ms = 60000;
	int num_spec_device = 100;   //TODO:由10修改为100
	state_arr_reset_single(num_spec_device,max_target_save, curr_device_timestamp_, arr_reset_time_ms);

    LogInfo("Num_radar={},num_droneID={},num_spectrum={} ",
            MultiSensorTargetInput->RadarTargetNum, MultiSensorTargetInput->SpectrumPositioningTargetNum,
            MultiSensorTargetInput->SpectrumTargetNum);

    int curr_sensor_type = 10;
    if (MultiSensorTargetInput->RadarTargetNum > 0) {
        curr_sensor_type = Radar;
    }
    else if (MultiSensorTargetInput->SpectrumPositioningTargetNum > 0) {
        curr_sensor_type = DroneID;
    }
    else if (MultiSensorTargetInput->SpectrumTargetNum > 0) {
        curr_sensor_type = Spectrum;
    }


    if (param_from_json_.test_mod_open == 1) {
        // 测试模式下，异步存储数据
        if (!async_data_saver_.save_data_async(MultiSensorTargetInput)) {
            LogWarn("Failed to enqueue data for async saving");
        }
    }

    FusionType fusion_type;
    Load_Multisensor_Data load_multisensor_data;

    load_multisensor_data.get_param(max_target_save, max_spectrum_save,&fusion_param);
    int load_flag = load_multisensor_data.load_target_data_work(MultiSensorTargetInput, droneID_deocde_info_arr_,radar_target_info_arr_, 
                                                                multi_spectrum_target_info_arr_, fusion_target_save_arr_,fusion_type);

    //打印环形存储器的所有数据
    LogInfo("--------------------max_target_num_ ------------------- = {}", max_target_save);

    if (load_flag < 0) {
        return -1;
    }
    save_spec_station_info(&multi_sensor_info_arr_, &multi_sensor_info_save_arr_, 32);

    //算法运行
    int spec_index = search_spec_device_index(multi_spectrum_target_info_arr_, max_spectrum_save, max_target_save);

    Timestamp_Diff time_stamp_diff_arr;
	load_multisensor_data.load_target_newst_timestamp(droneID_deocde_info_arr_, 
		radar_target_info_arr_, multi_spectrum_target_info_arr_[spec_index].spectrum_target_info_arr_,
        &time_stamp_diff_arr);

    // 根据时间差阈值，确认目标是否可以融合，判断融合类型
    JudgeValidTarget judge_valid_target;
    judge_valid_target.get_time_thre(fusion_param.droneID_time_thres, fusion_param.spectrum_time_thres,
                                     fusion_param.radar_time_thres);

    judge_valid_target.judge_use_timestamp_diff(&time_stamp_diff_arr, fusion_type);
    // 与已有的最新设备数据遍历比较融合判断

    // 根据融合类型确定不同的方法
    FusionMultiAlg fusion_mult_alg;
    fusion_mult_alg.get_fusion_param(fusion_param, max_target_save,multi_sensor_info_arr_.SpectrumDeviceInfoArr,
        multi_sensor_info_arr_.RadarDeviceInfoArr,fusion_target_save_arr_);

	// TDOA+协议破解+雷达 ，不同的组合配置不同的参数
	int fusion_ret = fusion_mult_alg.fusion_multi_alg_work(droneID_deocde_info_arr_, 
                                    radar_target_info_arr_,
		                            multi_spectrum_target_info_arr_[spec_index].spectrum_target_info_arr_, 
                                    FusionTargetInfoOutput,fusion_type);
	if (FusionTargetInfoOutput->uavNum >= 16|| fusion_ret<0) {
		LogError("Error, number of target is {} more than 16! reset all arr", FusionTargetInfoOutput->uavNum);
		return -1;
	}

    int save_flag = save_fusion_info(FusionTargetInfoOutput, max_target_save, curr_sensor_type);

    float center_latlon[2] = { 0 };
    get_center_latlon(&multi_sensor_info_save_arr_, center_latlon);
    fusion_mult_alg.ID_rearrange(FusionTargetInfoOutput,radar_target_info_arr_, center_latlon);

    interp_timer_.set_fusion_target_info_arr(fusion_target_save_arr_);
    interp_timer_.set_latlon_ref(center_latlon);
    //std::this_thread::sleep_for(std::chrono::seconds(1));

    //打印融合目标信息
    printf_fusion_target_info(FusionTargetInfoOutput);
    if (param_from_json_.test_mod_open == 1)
    {
        save_fusion_target2file(FusionTargetInfoOutput);
    }
	// 如果雷达长时间融合不上,丢批处理
	int reset_flag = state_rejudge( max_target_save, curr_device_timestamp_);
	for (int i = 0; i < max_target_save; i++) {
		if (fusion_target_save_arr_[i].radar_save_last_time>0 || fusion_target_save_arr_[i].droneID_save_last_time > 0) {
			LogDebug("i = {},radar_last_timestamp={},droneID_save_last_time={}", i,fusion_target_save_arr_[i].radar_save_last_time,
                fusion_target_save_arr_[i].droneID_save_last_time);
		}
	}
	
	LogInfo("--------------Fusion alg done--------------\n");
	return fusion_ret;
}

int fusion_alg::fusion_Alg_Compute_KUERLE(void *uav_alg_handle, MultiSensorTargetInputInfoArr *MultiSensorTargetInput,
                                          FusionTargetInfoArr *FusionTargetInfoOutput, OutputTarget_Callback callBackTmp, int32_t *fusion_status, FusionParam &fusion_param)
{
    try
    {
        printf_input_target_info(MultiSensorTargetInput);
        // 数据保存
        if (param_from_json_.test_mod_open == 1)
        {
            // 测试模式下，异步存储数据
            if (!async_data_saver_.save_data_async(MultiSensorTargetInput)) {
                LogWarn("Failed to enqueue data for async saving in KUERLE mode");
            }
        }

        // 从输入中获取数据加载到缓存
        Load_Multisensor_Data_Kuerle load_multisensor_data_kel;
        load_multisensor_data_kel.set_param(fusion_param);

        // 重置所有目标(超时)状态
        load_multisensor_data_kel.state_arr_reset_all(low_altitude_radar_target_arr_,
                                                    ground_radar_target_arr_,
                                                    adsB_target_arr_,
                                                    fusion_mult_alg_kel);

        // 读取目标数据
        load_multisensor_data_kel.load_target_data_work(MultiSensorTargetInput,
                                                        fusion_mult_alg_kel,
                                                        low_altitude_radar_target_arr_,
                                                        ground_radar_target_arr_,
                                                        adsB_target_arr_);

        // 设置参数、设置输出的目标
        fusion_mult_alg_kel.set_param(&fusion_param);

        // 地面雷达去重 通过轨迹的距离、运动方向、设备id来去重，并且维护去重前后、跨设备运动的唯一id
        fusion_mult_alg_kel.ground_radar_target_arr_remove_repeat(ground_radar_target_arr_, ground_radar_repeat_target_arr_, multi_sensor_info_arr_);

        // 多个传感器的目标去重
        fusion_mult_alg_kel.multi_sensor_target_arr_remove_repeat(low_altitude_radar_target_arr_, ground_radar_repeat_target_arr_, adsB_target_arr_);

        // 此处repeat_mult_result会为空  融合信息更新
        fusion_mult_alg_kel.update_fusion_result(low_altitude_radar_target_arr_, ground_radar_repeat_target_arr_, ground_radar_target_arr_, adsB_target_arr_, fusion_mult_alg_kel.repeat_gr2gr_result, fusion_mult_alg_kel.repeat_mult_result);

        // 输出融合结果
        fusion_mult_alg_kel.output_fusion_result(MultiSensorTargetInput, fusion_mult_alg_kel.repeat_mult_result, fusion_mult_alg_kel.repeat_gr2gr_result, FusionTargetInfoOutput);

        // 输出数据ID检查
        fusion_mult_alg_kel.check_fusion_ID(FusionTargetInfoOutput);

        // 输出repeat_result检查
        #ifdef AlgTest
        // fusion_mult_alg_kel.check_repeat_result(fusion_mult_alg_kel.repeat_mult_result);
        // printf_repeat_result(fusion_mult_alg_kel.repeat_mult_result, fusion_mult_alg_kel.repeat_gr2gr_result);
        #endif
        print_kuerle(low_altitude_radar_target_arr_, ground_radar_repeat_target_arr_, adsB_target_arr_);
        printf_fusion_target_info(FusionTargetInfoOutput);
    }
    catch (const std::exception& e)
    {
        // 直接将输入数据输出，不做处理
        LogError("Error: {}", e.what());
        fusion_mult_alg_kel.output_fusion_result_from_input(MultiSensorTargetInput, FusionTargetInfoOutput);
        return 1;
    }
    return 1;
}

int fusion_alg::fusion_Alg_Compute(void *uav_alg_handle, MultiSensorTargetInputInfoArr* MultiSensorTargetInput, 
	FusionTargetInfoArr* FusionTargetInfoOutput,OutputTarget_Callback callBackTmp, int32_t* fusion_status) {
	get_device_timestamp(curr_device_timestamp_);
    // 获取融合参数
    FusionParam fusion_param;
    get_fusion_param_struct(&fusion_param);
     // 0: 重庆机场  1: 库尔勒机场
    if (fusion_param.airport_type == CHONGQING) {
        LogInfo("Airport type: Chongqing");
        int ret = fusion_Alg_Compute_CHONGQING(uav_alg_handle, MultiSensorTargetInput, FusionTargetInfoOutput, callBackTmp, fusion_status, fusion_param);
        return ret;
    }
    else if (fusion_param.airport_type == KUERLE) {
        LogInfo("Airport type: Kuerle");
        int ret = fusion_Alg_Compute_KUERLE(uav_alg_handle, MultiSensorTargetInput, FusionTargetInfoOutput, callBackTmp, fusion_status, fusion_param);
        return ret;
    }

    LogError("Unknown airport type: {}", static_cast<int>(fusion_param.airport_type));
    return -1;
}


int fusion_alg::fusion_alg_close() {
    spdlog::drop_all();
    return 0;
}

QueueStats fusion_alg::get_async_saver_stats() const {
    return async_data_saver_.get_queue_stats();
}
